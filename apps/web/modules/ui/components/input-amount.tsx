"use client";

import { cn } from "@ui/lib";
import type React from "react";
import { forwardRef, useEffect, useState } from "react";
import type { Control, FieldPath, FieldValues } from "react-hook-form";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "./form";
import { Input } from "./input";
import { Label } from "./label";

export const centimesToEuros = (centimes: number | bigint): string => {
  if (!centimes) {
    return "";
  }
  return (BigInt(centimes) / BigInt(100)).toFixed(2).replace(".", ",");
};

interface FormFieldAmountProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> {
  control: Control<TFieldValues>;
  name: TName;
  label?: string;
  description?: string;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
}

interface FormAmountFieldProps
  extends Omit<
    React.InputHTMLAttributes<HTMLInputElement>,
    "value" | "onChange"
  > {
  label?: string;
  error?: string;
  value?: number; // Valeur en centimes
  onChange?: (centimes: number) => void;
}

export const FormAmountFieldUncontrolled = forwardRef<
  HTMLInputElement,
  FormAmountFieldProps
>(
  (
    {
      label,
      error,
      value = 0,
      onChange,
      className,
      placeholder = "0,00",
      ...props
    },
    ref
  ) => {
    const [displayValue, setDisplayValue] = useState("");
    const [isFocused, setIsFocused] = useState(false);

    // Convertir euros en centimes
    const eurosToCentimes = (euros: string): number => {
      const cleanValue = euros.replace(",", ".").replace(/[^\d.-]/g, "");
      const numValue = Number.parseFloat(cleanValue);
      return Number.isNaN(numValue) ? 0 : Math.round(numValue * 100);
    };

    // Mettre à jour l'affichage quand la valeur change
    useEffect(() => {
      if (!isFocused) {
        setDisplayValue(centimesToEuros(value));
      }
    }, [value, isFocused]);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value;
      setDisplayValue(inputValue);

      // Convertir et notifier le changement
      const centimes = eurosToCentimes(inputValue);
      onChange?.(centimes);
    };

    const handleFocus = () => {
      setIsFocused(true);
    };

    const handleBlur = () => {
      setIsFocused(false);
      // Reformater la valeur à la perte de focus
      setDisplayValue(centimesToEuros(value));
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      // Permettre seulement les chiffres, virgule, point, backspace, delete, tab, enter, flèches
      const allowedKeys = [
        "Backspace",
        "Delete",
        "Tab",
        "Enter",
        "ArrowLeft",
        "ArrowRight",
        "ArrowUp",
        "ArrowDown",
        "Home",
        "End",
      ];

      if (allowedKeys.includes(e.key)) return;

      if (e.key === "," || e.key === ".") {
        // Permettre seulement une virgule/point
        if (displayValue.includes(",") || displayValue.includes(".")) {
          e.preventDefault();
        }
        return;
      }

      // Permettre seulement les chiffres
      if (!/^\d$/.test(e.key)) {
        e.preventDefault();
      }
    };

    return (
      <div className={cn("space-y-2", className)}>
        {label && (
          <Label htmlFor={props.id} className="text-sm font-medium">
            {label}
            {props.required && <span className="text-red-500 ml-1">*</span>}
          </Label>
        )}
        <div className="relative">
          <Input
            {...props}
            ref={ref}
            type="text"
            value={displayValue}
            onChange={handleInputChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            className={cn(
              "pr-8",
              error && "border-red-500 focus-visible:ring-red-500"
            )}
            aria-invalid={!!error}
            aria-describedby={error ? `${props.id}-error` : undefined}
          />
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            <span className="text-sm text-muted-foreground">€</span>
          </div>
        </div>
        {error && (
          <p id={`${props.id}-error`} className="text-sm text-red-500">
            {error}
          </p>
        )}
      </div>
    );
  }
);
FormAmountFieldUncontrolled.displayName = "FormAmountField";

export function InputAmount<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  control,
  name,
  label,
  description,
  placeholder,
  disabled,
  required,
}: FormFieldAmountProps<TFieldValues, TName>) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field, fieldState }) => (
        <FormItem>
          {label && <FormLabel>{label}</FormLabel>}
          <FormControl>
            <FormAmountFieldUncontrolled
              {...field}
              id={name}
              placeholder={placeholder}
              disabled={disabled}
              required={required}
              error={fieldState.error?.message}
            />
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
