import React from "react";

interface DataItem {
  label: string;
  value: string | number;
  format?: "currency" | "number" | "text";
}

interface DataStatsSidebarEBProps {
  data?: DataItem[];
}

export function DataStatsSidebarEB({ data }: DataStatsSidebarEBProps) {
  if (!data || data.length === 0) return null;

  const formatValue = (item: DataItem) => {
    if (item.format === "currency") {
      return typeof item.value === "number"
        ? item.value.toLocaleString("fr-FR", {
            style: "currency",
            currency: "EUR",
            maximumFractionDigits: 0,
          })
        : item.value;
    }
    if (item.format === "number") {
      return typeof item.value === "number"
        ? item.value.toLocaleString("fr-FR")
        : item.value;
    }
    return item.value;
  };

  return (
    <>
      <div className="p-4">
        <div className="text-xs font-bold mb-2 text-center text-white">
          STATISTIQUES
        </div>
        <div className="text-xs space-y-1">
          {data.map((item, index) => (
            <div key={index} className="flex justify-between text-white">
              <span>{item.label}:</span>
              <span>{formatValue(item)}</span>
            </div>
          ))}
        </div>
      </div>
    </>
  );
}
