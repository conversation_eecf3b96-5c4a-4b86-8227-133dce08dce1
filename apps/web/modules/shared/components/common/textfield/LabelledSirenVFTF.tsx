"use client";
import { useCompanyInfo } from "@shared/hooks/services/useCompanyInfo";
import { FormItem, FormLabel } from "@ui/components/form";
import { Input } from "@ui/components/input";
import { cn } from "@ui/lib";
import { Globe, Info } from "lucide-react";
import React, { useEffect, useImperativeHandle, useRef, useState } from "react";
import { CompanyInfoDialog } from "../CompanyInfoDialog";
import { isValidSiren } from "../form/util";

interface LabelledSirenVFTFProps {
  label: string;
  name?: string;
  onCompanyFound?: (value: string, companyData?: any) => void;
}

export const LabelledSirenVFTF = React.forwardRef<
  HTMLInputElement,
  React.ComponentProps<"input"> & LabelledSirenVFTFProps
>(({ label, onCompanyFound, onChange, className, ...props }, ref) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const [value, setValue] = useState<string>("");

  const [loading, setLoading] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);
  const [companyData, setCompanyData] = useState<any>(null);
  const [showInfoDialog, setShowInfoDialog] = useState(false);
  const { searchBySiren } = useCompanyInfo();

  // Search for company information
  const handleSearchClick = async () => {
    if (!value) return;
    const cleanValue = value?.toString().replace(/[^\d.-]+/g, "");
    if (cleanValue) {
      setLoading(true);
      setSearchError(null);

      try {
        const { data: companyData } = await searchBySiren({
          siren: Number.parseInt(cleanValue),
        });
        if (companyData) {
          setCompanyData(companyData);
          setShowInfoDialog(true);
          // clean value in form state
          if (inputRef?.current) inputRef.current.value = cleanValue;
          onCompanyFound?.(cleanValue, companyData);
        } else {
          setSearchError("SIREN non trouvé");
        }
      } catch (error) {
        console.error("Error searching SIREN:", error);
        setSearchError("Erreur lors de la recherche");
      } finally {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    // Must set the initial state of the input based on the ref
    if (inputRef.current) setValue(inputRef.current.value);
  }, []);

  useImperativeHandle(ref, () => inputRef?.current as HTMLInputElement);

  return (
    <FormItem className={cn(loading ? "input-disabled" : "")}>
      <FormLabel htmlFor={props.name}>
        {label}
        {props?.required && <span className="text-red-500 ml-1">*</span>}
      </FormLabel>
      <div className="flex items-center gap-2">
        <Input
          ref={inputRef}
          type="number"
          className={cn("input-base", className)}
          placeholder="XXX XXX XXX"
          {...props}
          readOnly={props.disabled}
          onChange={(e) => {
            setValue(e.target.value);
            onChange?.(e);
          }}
        />
        {isValidSiren(value) && (
          <div className="flex items-center gap-1">
            {companyData && (
              <button
                onClick={() => setShowInfoDialog(true)}
                className="text-gray-400 hover:text-blue-600 transition-colors p-1 rounded hover:bg-blue-50"
                title="Voir les informations"
                type="button"
              >
                <Info className="h-4 w-4" />
              </button>
            )}
            <button
              onClick={handleSearchClick}
              className={
                "text-gray-400 hover:text-blue-600 transition-colors p-1 rounded hover:bg-blue-50"
              }
              disabled={loading}
              title="Rechercher sur data.gouv.fr"
              type="button"
            >
              <Globe className="h-4 w-4" />
            </button>
          </div>
        )}
        {loading && (
          <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
        )}
      </div>

      {searchError && (
        <p className="mt-1 text-xs text-red-600">{searchError}</p>
      )}

      {/* Modale d'informations */}
      {companyData && (
        <CompanyInfoDialog
          open={showInfoDialog}
          onClose={() => setShowInfoDialog(false)}
          data={companyData}
        />
      )}
    </FormItem>
  );
});
