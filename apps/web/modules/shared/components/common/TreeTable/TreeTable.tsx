import { PaginationWithLinks } from "@ui/components/navigation-with-links";
import { cn } from "@ui/lib";
import { ChevronDown, ChevronRight } from "lucide-react";
import React, { useEffect, useRef, useState } from "react";
import { definitions } from "./TreeTableFields";
import type { SortConfig, TreeTableProps } from "./types";

export function TreeTable<T>({
  data,
  columns,
  selectedRows = new Set(),
  onRowClick,
  idField = "id" as keyof T,
  onRowSelectChange,
  expandedRows = new Set(),
  renderExpanded,
  sumConfig,
  className = "",
  pagination,
}: TreeTableProps<T>) {
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    key: "",
    direction: "asc",
  });
  const [columnWidths] = useState<number[]>(
    columns.map((col) => Number(col.width) || 100)
  );
  const [focusedRowIndex, setFocusedRowIndex] = useState<number>(-1);
  const [lastSelectedIndex, setLastSelectedIndex] = useState<number>(-1);
  const tableRef = useRef<HTMLTableElement>(null);

  // Handle keyboard navigation and selection
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!tableRef.current?.contains(document.activeElement)) return;

      switch (e.key) {
        case "ArrowUp":
          e.preventDefault();
          setFocusedRowIndex((prev) => Math.max(0, prev - 1));
          if (!e.shiftKey) setLastSelectedIndex(-1);
          break;

        case "ArrowDown":
          e.preventDefault();
          setFocusedRowIndex((prev) => Math.min(data.length - 1, prev + 1));
          if (!e.shiftKey) setLastSelectedIndex(-1);
          break;

        case " ":
          e.preventDefault();
          if (focusedRowIndex >= 0) {
            const rowId = String(data[focusedRowIndex][idField]);
            // Si la touche Ctrl/Cmd est pressée, on sélectionne
            if (e.ctrlKey || e.metaKey) {
              const newSelect = new Set(selectedRows);
              if (newSelect.size === 1 && selectedRows.has(rowId)) {
                newSelect.clear();
              } else {
                newSelect.clear();
                newSelect.add(rowId);
              }
              onRowSelectChange?.({
                selectedRows: newSelect,
              });
            }
            // Sinon on expand/collapse
            else if (onRowClick) {
              onRowClick(rowId, true, e as unknown as React.MouseEvent);
            }
          }
          break;

        case "Enter":
          e.preventDefault();
          if (focusedRowIndex >= 0 && onRowSelectChange) {
            const rowId = String(data[focusedRowIndex][idField]);
            const newSelect = getNewSelect(rowId, new Set(selectedRows));
            onRowSelectChange?.({
              selectedRows: newSelect,
            });
          }
          break;

        case "a":
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            if (onRowSelectChange) {
              const newSelect = new Set(selectedRows);
              data.forEach((row) => {
                const rowId = String(row[idField]);
                if (!newSelect.has(rowId)) {
                  newSelect.add(rowId);
                }
              });
              onRowSelectChange?.({
                selectedRows: newSelect,
              });
            }
          }
          break;
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [focusedRowIndex, data, onRowSelectChange, onRowClick, idField]);

  // Handle click outside to clear selection
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (tableRef.current && !tableRef.current.contains(e.target as Node)) {
        setFocusedRowIndex(-1);
        setLastSelectedIndex(-1);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleRowSelect = (
    index: number,
    row: T,
    isCheckboxClick: boolean,
    event: React.MouseEvent
  ) => {
    const rowId = String(row[idField]);

    const newSelect = new Set(selectedRows);

    setFocusedRowIndex(index);

    if (event.shiftKey && lastSelectedIndex >= 0) {
      const start = Math.min(lastSelectedIndex, index);
      const end = Math.max(lastSelectedIndex, index);

      for (let i = start; i <= end; i++) {
        if (i === lastSelectedIndex) continue;
        const currentRow = data[i];
        const currentRowId = String(currentRow[idField]);
        const validateId = getNewSelect(currentRowId, newSelect);
        onRowSelectChange?.({
          selectedRows: validateId,
        });
      }
    } else if (event.ctrlKey || event.metaKey) {
      const validateId = getNewSelect(rowId, newSelect);
      onRowSelectChange?.({
        selectedRows: validateId,
      });
      setLastSelectedIndex(index);
    } else {
      if (isCheckboxClick) {
        const validateId = getNewSelect(rowId, newSelect);
        onRowSelectChange?.({
          selectedRows: validateId,
        });
      }
      setLastSelectedIndex(index);
    }
  };

  const getNewSelect = (id: string, setter: Set<string>) => {
    const currentId = setter.has(id);
    currentId ? setter.delete(id) : setter.add(id);
    return setter;
  };

  const handleSelectAll = () => {
    if (!onRowSelectChange) return;

    if (selectedRows.size === data.length) {
      onRowSelectChange?.({
        selectedRows: new Set(),
      });
    } else {
      const allIds = new Set(data.map((row) => String(row[idField])));
      onRowSelectChange?.({ selectedRows: allIds });
    }
  };

  const handleExpand = (e: React.MouseEvent, rowId: string) => {
    e.preventDefault();
    e.stopPropagation();
    if (onRowClick) {
      onRowClick(rowId, true, e);
    }
  };

  return (
    <div
      className={`border rounded-lg border-gray-200 overflow-hidden bg-white ${className}`}
    >
      <div className="w-full overflow-x-auto">
        <table ref={tableRef} className="min-w-full divide-y divide-gray-200">
          <thead>
            <tr className="bg-lnfblue-500 text-white h-9 max-h-9 select-none">
              {/* Selection checkbox */}
              {onRowSelectChange && (
                <th className="w-8 px-3 max-h-9 text-left">
                  <div className="relative">
                    <input
                      type="checkbox"
                      checked={selectedRows.size === data?.length}
                      ref={(input) => {
                        if (input) {
                          input.indeterminate =
                            selectedRows.size > 0 &&
                            selectedRows.size < data.length;
                        }
                      }}
                      onChange={() => handleSelectAll()}
                      className="rounded border-white/30 bg-transparent checked:bg-white/90 checked:border-white/90 focus:ring-white/30"
                      title={
                        selectedRows.size === 0
                          ? "Sélectionner tout"
                          : selectedRows.size === data.length
                            ? "Désélectionner tout"
                            : `${selectedRows.size} sur ${data?.length} sélectionnés`
                      }
                    />
                  </div>
                </th>
              )}

              {/* Expansion column */}
              {renderExpanded && <th className="w-8 px-3" />}

              {/* Column headers */}
              {columns.map((column, index) => {
                const columnDef =
                  definitions[column.type || "default"] || definitions.default;
                const align = column.align || columnDef.align;
                return (
                  <th
                    key={index}
                    className={cn(
                      "px-3 py-2 text-xs font-semibold uppercase tracking-wider",
                      column.sortable && "cursor-pointer",
                      align === "left" && "text-left",
                      align === "center" && "text-center",
                      align === "right" && "text-right"
                    )}
                    style={{
                      width: columnWidths[index],
                      maxHeight: "2.25rem",
                    }}
                    onClick={() =>
                      column.sortable &&
                      setSortConfig((prev) => ({
                        key: column.key,
                        direction:
                          prev.key === column.key && prev.direction === "asc"
                            ? "desc"
                            : "asc",
                      }))
                    }
                  >
                    <div className="flex items-center justify-between gap-2">
                      <div className="flex-1 truncate">{column.header}</div>
                      {column.sortable && (
                        <div className="flex-shrink-0">
                          {sortConfig.key === column.key ? (
                            <div className="w-4 h-4 flex items-center justify-center">
                              <div
                                className={`w-0 h-0 border-l-[5px] border-l-transparent border-r-[5px] border-r-transparent ${
                                  sortConfig.direction === "asc"
                                    ? "border-b-[6px] border-b-white"
                                    : "border-t-[6px] border-t-white"
                                }`}
                              />
                            </div>
                          ) : (
                            <div className="w-4 h-4 flex flex-col items-center justify-center opacity-30">
                              <div className="w-0 h-0 border-l-[4px] border-l-transparent border-r-[4px] border-r-transparent border-b-[4px] border-b-white mb-[1px]" />
                              <div className="w-0 h-0 border-l-[4px] border-l-transparent border-r-[4px] border-r-transparent border-t-[4px] border-t-white" />
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </th>
                );
              })}
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {data?.map((row, index) => {
              const rowId = String(row[idField]);
              const isExpanded = expandedRows.has(rowId);
              const isSelected = selectedRows.has(rowId);
              const isFocused = index === focusedRowIndex;

              return (
                <React.Fragment key={rowId}>
                  <tr
                    className={`
                    text-[11px] transition-colors
                    ${isSelected ? "bg-blue-50 hover:bg-blue-100" : "hover:bg-gray-50"} 
                    ${isFocused ? "outline-2 outline-blue-500" : ""}
                    cursor-pointer select-none
                  `}
                    onClick={(e) => onRowClick?.(rowId, false, e)}
                    tabIndex={0}
                    aria-selected={isSelected}
                  >
                    {/* Selection checkbox */}
                    {onRowSelectChange && (
                      <td
                        className="w-8 px-3"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={(e) =>
                            handleRowSelect(
                              index,
                              row,
                              true,
                              e.nativeEvent as unknown as React.MouseEvent
                            )
                          }
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </td>
                    )}

                    {/* Expansion chevron */}
                    {renderExpanded && (
                      <td className="w-8 px-3">
                        <button
                          onClick={(e) => handleExpand(e, rowId)}
                          className="p-1 hover:bg-gray-100 rounded transition-colors"
                        >
                          {isExpanded ? (
                            <ChevronDown className="w-4 h-4 text-gray-500" />
                          ) : (
                            <ChevronRight className="w-4 h-4 text-gray-500" />
                          )}
                        </button>
                      </td>
                    )}

                    {/* Data columns */}
                    {columns.map((column, colIndex) => {
                      const columnDef =
                        definitions[column.type || "default"] ||
                        definitions.default;
                      return (
                        <td
                          key={colIndex}
                          className={"px-3 py-2"}
                          style={{
                            width: columnWidths[colIndex],
                            textAlign: column.align || columnDef.align,
                          }}
                        >
                          {column.render
                            ? column.render?.(row)
                            : columnDef.render({
                                key: column.key,
                                value: column.value
                                  ? column.value(row)
                                  : row[column.key],
                                row,
                              })}
                        </td>
                      );
                    })}
                  </tr>
                  {isExpanded && renderExpanded && (
                    <tr>
                      <td
                        colSpan={columns.length + (onRowSelectChange ? 2 : 1)}
                        className="bg-gray-50 p-4 border-t border-gray-100"
                      >
                        {renderExpanded(row)}
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              );
            })}
          </tbody>
          {sumConfig && (
            <tfoot>
              <tr className="border-t-2 border-blue-600 h-10 max-h-10 bg-gray-50">
                {/* Checkbox column */}
                {onRowSelectChange && <td className="w-8 px-3" />}

                {/* Expansion column */}
                {renderExpanded && <td className="w-8 px-3" />}

                {/* Data columns */}
                {columns.map((column, index) => {
                  const isCountField = column.key === sumConfig.countField;
                  const isSumField = sumConfig.sumFields?.includes(column.key);
                  const hasCustomSum = sumConfig.customSums?.[column.key];
                  const columnDef =
                    definitions[column.type || "default"] ||
                    definitions.default;

                  if (isCountField) {
                    return (
                      <td
                        key={index}
                        className="px-3 py-2 text-xs font-semibold text-gray-900"
                        style={{
                          width: columnWidths[index],
                          textAlign: column.align || ("left" as any),
                          maxHeight: "2.5rem",
                        }}
                      >
                        {data?.length}
                      </td>
                    );
                  }

                  if (isSumField || hasCustomSum) {
                    const sum = hasCustomSum
                      ? sumConfig.customSums?.[column.key](data)
                      : data?.reduce((acc, row: any) => {
                          const value = row[column.key];
                          if (
                            typeof value === "number" &&
                            !Number.isNaN(value)
                          ) {
                            return acc + value;
                          }
                          return acc;
                        }, 0);

                    return (
                      <td
                        key={index}
                        className="px-3 py-2 text-xs font-semibold text-blue-600"
                        style={{
                          width: columnWidths[index],
                          textAlign: column.align || ("right" as any),
                          maxHeight: "2.5rem",
                        }}
                      >
                        {typeof sum === "number" && !Number.isNaN(sum) ? (
                          column.key === "collaborators" ? (
                            <div className="text-center text-gray-900">
                              {sum.toLocaleString("fr-FR")}
                            </div>
                          ) : (
                            columnDef.render({
                              key: column.key,
                              value: sum,
                              row: {},
                            })
                          )
                        ) : (
                          ""
                        )}
                      </td>
                    );
                  }

                  return (
                    <td
                      key={index}
                      style={{
                        width: columnWidths[index],
                        maxHeight: "2.5rem",
                      }}
                    />
                  );
                })}
              </tr>
            </tfoot>
          )}
        </table>
      </div>
      {pagination && (
        <PaginationWithLinks
          {...pagination}
          selectedCount={selectedRows.size}
        />
      )}
    </div>
  );
}
