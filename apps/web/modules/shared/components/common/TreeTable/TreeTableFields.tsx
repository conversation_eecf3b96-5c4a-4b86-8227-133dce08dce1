export interface PropsField {
  key: string;
  row: any;
  value: any;
}

export const definitions = {
  default: {
    align: "left",
    render: ({ value }: PropsField) => value,
  },
  amount: {
    align: "right",
    render: ({ value }: PropsField) =>
      value !== undefined
        ? (BigInt(value || 0) / BigInt(100)).toLocaleString("fr-FR", {
            style: "currency",
            currency: "EUR",
          })
        : "0,00 €",
  },
  date: {
    align: "left",
    render: ({ value }: PropsField) =>
      value ? new Date(value).toLocaleDateString("fr-FR") : null,
  },
};
