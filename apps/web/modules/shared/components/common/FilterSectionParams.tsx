import { useListSearchParams } from "@shared/hooks/useListSearchParams";
import { useSearchParams } from "custom-router";
import { Search, X } from "lucide-react";
import { useEffect, useState } from "react";
import { useDataTableFilters } from "./data-table-filter";
import { ActiveFilters } from "./data-table-filter/components/active-filters";
import { FilterActions } from "./data-table-filter/components/filter-actions";
import { FilterSelector } from "./data-table-filter/components/filter-selector";
import type {
  ColumnConfig,
  FiltersState,
} from "./data-table-filter/core/types";
import { ActionButtons } from "./FilterSectionParamsActions";

// Définition des placeholders avec leurs variations
const PLACEHOLDERS = [
  {
    base: "Rechercher un contact ...",
    sub: "Rechercher un interlocuteur ...",
    column: "Rechercher par nom, email, téléphone ...",
  },
  {
    base: "Rechercher un devis ...",
    sub: "Trier par date, montant ...",
    column: "Rechercher par SIREN, code ...",
  },
  {
    base: "Rechercher un chantier ...",
    sub: "Trier par rentabilité, client ...",
    column: "Rechercher par nom, code ...",
  },
  {
    base: "Rechercher une commande émise ...",
    sub: "Trier par fornisseur, prestataire ...",
    column: "Rechercher par nom, code ...",
  },
  {
    base: "Rechercher une entreprise ...",
    sub: "Rechercher un particulier ...",
    column: "Rechercher par ville, adresse ...",
  },
  {
    base: "Rechercher une charge ...",
    sub: "Trier par fournisseur ...",
    column: "Rechercher par montant, chantier ...",
  },
  {
    base: "Rechercher une facture ...",
    sub: "Trier par projet, par date...",
    column: "Rechercher par montant, chantier ...",
  },
  {
    base: "Rechercher un actif de l'entreprise ...",
    sub: "Trier par type, par état ...",
    column: "Rechercher par montant, désignation ...",
  },
  {
    base: "Rechercher un collaborateur ...",
    sub: "Trier par nom, service, fonction ...",
    column: "Rechercher par nom, fonction ...",
  },
];

interface FilterSectionParamsProps<T> {
  defaultFilters?: FiltersState;
  onFilterChange: (filter: FiltersState) => void;
  onSearch?: (value: string) => void;
  selectedRows?: Set<string>;
  onAdd?: () => void;
  onAddSub?: () => void;
  onEdit?: (itemId: any) => void;
  onSendEmail?: () => void;
  onCombineDocuments?: () => void;
  onExportExcel?: () => void;
  onSchedule?: () => void;
  onImport?: () => void;
  onDelete?: () => void;
  /**
   * @description
   * Set of column configurations used for filtering within the table
   * @example
   * ```ts
   * import { createColumnConfigHelper } from "@shared/components/common/data-table-filter/core/filters";
   * import { User } from "lucide-react";
   *
   * const dtf = createColumnConfigHelper<T>();
   * const columnsConfig = [
   *   dtf
   *     .text()
   *     .id("firstName")
   *     .accessor((row) => row.firstName)
   *     .displayName("Nom")
   *     .icon(User)
   *     .build(),
   * ] as const;
   * ```
   */
  columnsConfig?: ReadonlyArray<ColumnConfig<T, any, any, any>>;
  placeholderSetIndex?: number; // ← ajout ici
}

export function FilterSectionParams<T>({
  defaultFilters,
  onFilterChange,
  onSearch,
  selectedRows,
  onAdd,
  onAddSub,
  onEdit,
  onSendEmail,
  onCombineDocuments,
  onExportExcel,
  onSchedule,
  onImport,
  onDelete,
  columnsConfig,
  placeholderSetIndex = 0, // ← valeur par défaut : contacts
}: FilterSectionParamsProps<T>) {
  const { listParams } = useListSearchParams();
  const [searchValue, setSearchValue] = useState(listParams.q || "");
  const [loading, setLoading] = useState(false);
  const [placeholder, setPlaceholder] = useState("");
  const [currentPlaceholderSet, setCurrentPlaceholderSet] =
    useState(placeholderSetIndex);
  const [currentPlaceholderType, setCurrentPlaceholderType] = useState(0);
  const searchParams = useSearchParams();

  // Animation du placeholder avec useEffect
  useEffect(() => {
    if (loading) return;

    const targetText =
      PLACEHOLDERS[currentPlaceholderSet][
        ["base", "sub", "column"][currentPlaceholderType]
      ];

    let currentIndex = 0;
    let typingTimer: NodeJS.Timeout | null = null;
    let pauseTimer: NodeJS.Timeout | null = null;

    const typeNextChar = () => {
      if (currentIndex < targetText.length) {
        setPlaceholder(targetText.slice(0, currentIndex + 1));
        currentIndex++;
        typingTimer = setTimeout(typeNextChar, 50);
      } else {
        pauseTimer = setTimeout(moveToNext, 1500);
      }
    };

    const moveToNext = () => {
      currentIndex = 0;
      const nextType = (currentPlaceholderType + 1) % 3;
      let nextSet = currentPlaceholderSet;

      if (nextType === 0) {
        nextSet = (currentPlaceholderSet + 1) % PLACEHOLDERS.length;
      }

      setCurrentPlaceholderType(nextType);
      setCurrentPlaceholderSet(nextSet);
      typingTimer = setTimeout(typeNextChar, 50);
    };

    typingTimer = setTimeout(typeNextChar, 50);

    return () => {
      if (typingTimer) clearTimeout(typingTimer);
      if (pauseTimer) clearTimeout(pauseTimer);
    };
  }, [loading, currentPlaceholderSet, currentPlaceholderType]);

  function updateSearchParams(q?: string) {
    const params = new URLSearchParams(searchParams.toString());

    if (q) {
      params.set("q", q);
    } else {
      params.delete("q");
    }

    const queryString = params.toString();
    const newUrl = queryString
      ? `${window.location.pathname}?${queryString}`
      : window.location.pathname;

    if (window.location.search !== (queryString ? `?${queryString}` : "")) {
      window.history.pushState(null, "", newUrl);
    }
  }

  // Gérer la recherche avec debounce
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setLoading(true);
      onSearch?.(searchValue);
      updateSearchParams(searchValue);
      setLoading(false);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchValue, onSearch]);

  const [filtersState, setFiltersState] = useState<FiltersState>(
    defaultFilters ?? []
  );

  const { columns, filters, actions, strategy } = useDataTableFilters({
    strategy: "server",
    data: [],
    onFiltersChange: setFiltersState,
    filters: filtersState,
    columnsConfig: columnsConfig ?? [],
  });

  useEffect(() => {
    onFilterChange(filtersState);
  }, [filtersState]);

  return (
    <div className="flex flex-wrap gap-4 items-center justify-between w-full">
      {/* Zone de recherche et filtre */}
      <div className="flex-1 flex gap-4 items-center flex-wrap">
        <div className="relative w-[300px]">
          <input
            type="text"
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            placeholder={loading ? "Recherche en cours..." : placeholder}
            className={`
              w-full pl-10 pr-10 py-2 rounded-lg 
              border border-gray-200 
              text-gray-900 placeholder-gray-400
              focus:ring-2 focus:ring-blue-500 focus:border-transparent 
              text-sm bg-white
              ${loading ? "text-gray-500 italic" : ""}
            `}
            disabled={loading}
          />
          <Search
            className={`absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 ${
              loading ? "text-gray-400 animate-pulse" : "text-gray-400"
            }`}
          />
          {searchValue && (
            <button
              type="button"
              onClick={() => setSearchValue("")}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100 transition-colors"
              disabled={loading}
            >
              <X className="w-4 h-4" />
            </button>
          )}
        </div>

        <div className="flex flex-wrap gap-2 w-full flex-1 items-center">
          <FilterSelector
            columns={columns}
            filters={filters}
            actions={actions}
            strategy={strategy}
            locale="fr" // change local here
          />
          <ActiveFilters
            columns={columns}
            filters={filters}
            actions={actions}
            strategy={strategy}
            locale="fr" // change local here
          />
        </div>
      </div>
      <FilterActions
        hasFilters={filters.length > 0}
        actions={actions}
        locale="fr" // change local here
      />

      {/* Action buttons */}
      <ActionButtons
        selectedRows={selectedRows}
        onAdd={onAdd}
        onAddSub={onAddSub}
        onEdit={onEdit}
        onSendEmail={onSendEmail}
        onCombineDocuments={onCombineDocuments}
        onExportExcel={onExportExcel}
        onSchedule={onSchedule}
        onImport={onImport}
        onDelete={onDelete}
      />
    </div>
  );
}
