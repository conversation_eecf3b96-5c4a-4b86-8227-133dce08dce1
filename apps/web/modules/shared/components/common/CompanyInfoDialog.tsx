import { Dialog } from "@mui/material";
import {
  Building2,
  Calendar,
  Euro,
  FileText,
  Globe,
  Info,
  Mail,
  MapPin,
  Phone,
  X,
} from "lucide-react";
import React from "react";

interface CompanyInfoDialogProps {
  open: boolean;
  onClose: () => void;
  data: any;
}

export function CompanyInfoDialog({
  open,
  onClose,
  data,
}: CompanyInfoDialogProps) {
  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <div className="p-6">
        {/* En-tête */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center">
              <Building2 className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold">{data.nom_entreprise}</h2>
              <p className="text-sm text-gray-600">
                SIREN {data.siren_formate}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Informations gratuites */}
        <div className="space-y-6">
          <div>
            <h3 className="text-sm font-medium text-gray-900 mb-4">
              Informations gratuites
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Building2 className="w-4 h-4 text-gray-400" />
                  <span className="text-sm font-medium text-gray-700">
                    Forme juridique
                  </span>
                </div>
                <p className="text-sm text-gray-600">{data.forme_juridique}</p>
              </div>

              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Calendar className="w-4 h-4 text-gray-400" />
                  <span className="text-sm font-medium text-gray-700">
                    Date de création
                  </span>
                </div>
                <p className="text-sm text-gray-600">
                  {data.date_creation_formate}
                </p>
              </div>

              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <FileText className="w-4 h-4 text-gray-400" />
                  <span className="text-sm font-medium text-gray-700">
                    Code NAF
                  </span>
                </div>
                <p className="text-sm text-gray-600">
                  {data.code_naf} - {data.libelle_code_naf}
                </p>
              </div>

              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <MapPin className="w-4 h-4 text-gray-400" />
                  <span className="text-sm font-medium text-gray-700">
                    Adresse
                  </span>
                </div>
                <p className="text-sm text-gray-600">
                  {data.siege?.adresse_ligne_1}
                  <br />
                  {data.siege?.code_postal} {data.siege?.ville}
                </p>
              </div>

              {data.capital && (
                <div className="p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Euro className="w-4 h-4 text-gray-400" />
                    <span className="text-sm font-medium text-gray-700">
                      Capital
                    </span>
                  </div>
                  <p className="text-sm text-gray-600">
                    {data.capital_formate}
                  </p>
                </div>
              )}

              {data.telephone && (
                <div className="p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Phone className="w-4 h-4 text-gray-400" />
                    <span className="text-sm font-medium text-gray-700">
                      Téléphone
                    </span>
                  </div>
                  <p className="text-sm text-gray-600">{data.telephone}</p>
                </div>
              )}

              {data.email && (
                <div className="p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Mail className="w-4 h-4 text-gray-400" />
                    <span className="text-sm font-medium text-gray-700">
                      Email
                    </span>
                  </div>
                  <p className="text-sm text-gray-600">{data.email}</p>
                </div>
              )}

              {data.sites_internet?.length > 0 && (
                <div className="p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Globe className="w-4 h-4 text-gray-400" />
                    <span className="text-sm font-medium text-gray-700">
                      Site web
                    </span>
                  </div>
                  <p className="text-sm text-gray-600">
                    {data.sites_internet[0]}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Informations payantes */}
          <div>
            <h3 className="text-sm font-medium text-gray-900 mb-4">
              Informations payantes (1 token)
            </h3>
            <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
              <div className="flex items-start gap-3">
                <Info className="w-5 h-5 text-blue-500 flex-shrink-0 mt-0.5" />
                <div className="space-y-2">
                  <p className="text-sm text-gray-600">
                    Les informations suivantes sont disponibles avec un token :
                  </p>
                  <ul className="space-y-1 text-sm text-gray-600">
                    <li>• Dirigeants et mandataires sociaux</li>
                    <li>• Bénéficiaires effectifs</li>
                    <li>• Documents officiels</li>
                    <li>• Données financières</li>
                    <li>• Annonces légales</li>
                    <li>• Procédures collectives</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Dialog>
  );
}
