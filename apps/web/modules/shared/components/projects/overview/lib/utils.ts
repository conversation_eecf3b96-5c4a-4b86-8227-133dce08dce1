import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatCurrency(value: number | string | undefined): string {
  if (value === undefined) return "0 €";

  const numValue =
    typeof value === "string"
      ? Number.parseFloat(
          value.replace(/\s+/g, "").replace(",", ".").replace("€", "")
        )
      : value;

  if (Number.isNaN(numValue)) return "0 €";

  return new Intl.NumberFormat("fr-FR", {
    style: "currency",
    currency: "EUR",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(numValue);
}

export function parsePercentage(value: string | number | undefined): number {
  if (value === undefined) return 0;

  const strValue = typeof value === "number" ? value.toString() : value;
  const cleanValue = strValue.replace("%", "").replace(",", ".").trim();

  const numValue = Number.parseFloat(cleanValue);
  return Number.isNaN(numValue) ? 0 : numValue / 100;
}

export function formatPercentage(value: number): string {
  return `${(value * 100).toFixed(2)}%`;
}

export function calculateTreasury(
  paid: string | undefined,
  total: string | undefined
): number {
  if (!paid || !total) return 0;
  const paidAmount = Number.parseFloat(paid.replace(/[^\d.-]/g, ""));
  const totalAmount = Number.parseFloat(total.replace(/[^\d.-]/g, ""));
  return paidAmount - totalAmount;
}
