import {
  TExpenseCreateSchema,
  TExpenseUpdateSchema,
} from "@repo/api/validation";
import type { TExpense } from "@repo/database/client";
import type { ModelEditorBookProps } from "@repo/types";
import {
  EditorBook,
  type EditorBookProps,
} from "@shared/components/common/editorbook2";
import { useLInvoiceType } from "@shared/hooks/models";
import { FileText, Send, Upload } from "lucide-react";
import { useMemo, useState } from "react";
import { z } from "zod";
import { EnvoiTab } from "./tabs/EnvoiTab";
import { InformationsTab } from "./tabs/InformationsTab";
import { NumerisationTab } from "./tabs/NumerisationTab";

export function ExpenseEB({
  isOpen,
  onClose,
  onCreate,
  onUpdate,
  item: expense,
  activeTab = "informations",
}: ModelEditorBookProps<Omit<TExpense, "organizationId">>) {
  const [activeItem, setActiveItem] = useState(activeTab);
  const [isModified, setIsModified] = useState(false);

  const menuItems = [
    {
      id: "informations",
      icon: FileText,
      label: "Informations",
      url: expense?.id ? `/expenses/${expense?.id}/` : "",
    },
    {
      id: "numerisation",
      icon: Upload,
      label: "Numérisation",
      url: expense?.id ? `/expenses/${expense?.id}?tab=numerisation` : "",
    },
    {
      id: "suivi",
      icon: Send,
      label: "Suivi",
      url: expense?.id ? `/expenses/${expense?.id}?tab=suivi` : "",
    },
  ];

  const handleSave = async (data: any) => {
    console.info("handleSave", { data });
    setIsModified(false);
    expense?.id ? await onUpdate?.(data) : await onCreate?.(data);
    onClose();
  };

  const handleCancel = () => {
    setIsModified(false);
    onClose();
  };

  const sidebarData = [
    {
      label: "Affectation",
      value: `0%`,
      format: "text",
    },
    {
      label: "Scan",
      value: "Non",
      format: "text",
    },
    {
      label: "Compta",
      value: "Non",
      format: "text",
    },
    {
      label: "Montant H.T.",
      value: expense?.amountExclTaxes,
      format: "currency",
    },
    {
      label: "T.V.A.",
      value: expense?.amountTaxes,
      format: "currency",
    },
    {
      label: "Montant T.T.C.",
      value:
        (expense?.amountExclTaxes || BigInt(0)) -
        (expense?.amountTaxes || BigInt(0)),
      format: "currency",
    },
    {
      label: "Solde à payer",
      value:
        (expense?.amountExclTaxes || BigInt(0)) -
        (expense?.amountTaxes || BigInt(0)),
      format: "currency",
    },
  ];
  return (
    <EditorBook
      resolver={
        expense?.id
          ? TExpenseCreateSchema.omit({ organizationId: true }).extend({
              files: z.array(z.any()).optional(),
            })
          : TExpenseUpdateSchema.omit({ organizationId: true }).extend({
              files: z.array(z.any()).optional(),
            })
      }
      values={expense}
      isOpen={isOpen}
      onClose={onClose}
      title="Dépense"
      code={expense?.senderReference || ""}
      name={expense?.description || ""}
      menuItems={menuItems}
      activeTab={activeItem}
      onTabChange={setActiveItem}
      status={{
        currentStatus: "draft",
        lastUpdateDate: expense?.updatedAt
          ? new Date(expense.updatedAt).toLocaleDateString()
          : "",
      }}
      headerBgColor="#B71C1C"
      sidebarBgColor="#B71C1C"
      activeItemBgColor="#7F0000"
      isModified={isModified}
      onSave={handleSave}
      onCancel={handleCancel}
      data={sidebarData}
    >
      {activeItem === "informations" && <InformationsTab />}
      {activeItem === "numerisation" && <NumerisationTab />}
      {activeItem === "suivi" && <EnvoiTab />}
    </EditorBook>
  );
}

type Props = {
  item?: TExpense | null;
};
export const useExpenseEB = (props?: Props) => {
  const invoiceTypes = useLInvoiceType({ autocomplete: true });
  const defaultValues = useMemo(() => {
    const factureOption = invoiceTypes.autocomplete.options.find(
      (option) => option.value === "invoice"
    );

    return {
      ...props?.item,
      documentTypeId: props?.item?.documentTypeId || factureOption?.value || "",
    };
  }, [props?.item, invoiceTypes.autocomplete.options]);

  const menuItems = [
    {
      id: "informations",
      icon: FileText,
      label: "Informations",
      content: <InformationsTab />,
    },
    {
      id: "numerisation",
      icon: Upload,
      label: "Numérisation",
      content: <NumerisationTab />,
    },
    {
      id: "suivi",
      icon: Send,
      label: "Suivi",
      content: <EnvoiTab />,
    },
  ];

  const sidebarData = [
    {
      label: "Affectation",
      value: `0%`,
      format: "text",
    },
    {
      label: "Scan",
      value: "Non",
      format: "text",
    },
    {
      label: "Compta",
      value: "Non",
      format: "text",
    },
    {
      label: "amount H.T.",
      value: props?.item?.amountExclTaxes,
      format: "currency",
    },
    {
      label: "T.V.A.",
      value: props?.item?.amountTaxes,
      format: "currency",
    },
    {
      label: "amount T.T.C.",
      value:
        BigInt(props?.item?.amountExclTaxes || 0) -
        BigInt(props?.item?.amountTaxes || 0),
      format: "currency",
    },
    {
      label: "Solde à payer",
      value:
        BigInt(props?.item?.amountExclTaxes || 0) -
        BigInt(props?.item?.amountTaxes || 0),
      format: "currency",
    },
  ];

  const defaultProps: Omit<EditorBookProps, "isOpen" | "onClose"> = {
    title: "Dépense",
    code: "",
    name: "",
    menuItems,
    data: sidebarData,
    headerBgColor: "#B71C1C",
    sidebarBgColor: "#B71C1C",
    activeItemBgColor: "#7F0000",
    children: <></>,
    isModified: false,
    onSave: async () => {},
    onCancel: () => {},
    status: {
      currentStatus: "draft",
      lastUpdateDate: props?.item?.updatedAt
        ? new Date(props.item.updatedAt).toLocaleDateString()
        : "",
    },
    resolver: props?.item?.id
      ? TExpenseUpdateSchema.omit({ organizationId: true }).extend({
          files: z.array(z.any()).optional(),
        })
      : TExpenseCreateSchema.omit({ organizationId: true }).extend({
          files: z.array(z.any()).optional(),
        }),
    values: defaultValues,
  };

  return {
    defaultProps,
  };
};
