import { $Enums, TCompanyContact } from "@repo/database/client";
import { LabelledCheckBox } from "@shared/components/common/textfield/LabelledCheckBox";
import { LabelledDateField } from "@shared/components/common/textfield/LabelledDateField";
import { LabelledSelectInput } from "@shared/components/common/textfield/LabelledSelectInput";
import { LabelledVTextField } from "@shared/components/common/textfield/LabelledVTextField";
import {
  useLInvoiceType,
  useTCompanyContact,
  useTFile,
  useTFileRelation,
} from "@shared/hooks/models";
import { useFormContext } from "@shared/hooks/useFormContext";
import { InputAmount } from "@ui/components/input-amount";
import { FileText, Mail, MapPin, Phone, User } from "lucide-react";
import React, { ReactNode, useEffect, useState } from "react";
import { BudgetDistribution } from "./BudgetDistribution";
import { DistributionByOrder } from "./DistributionByOrder";
import { DistributionByProject } from "./DistributionByProject";
import { HelpOnDocumentType } from "./HelpOnDocumentType";

export function InformationsTab() {
  const [isScanned, setIsScanned] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [contactInfo, setContactInfo] = useState<TCompanyContact>();
  const [previousVAT, setPreviousVAT] = useState<Record<string, string>>({});

  const { register, control, watch, setValue, setTouchedValue } =
    useFormContext();
  const invoiceTypes = useLInvoiceType({ autocomplete: true });
  const { autocomplete: companyContactAutocomplete } = useTCompanyContact({
    autocomplete: true,
  });

  const [uploading, setUploading] = useState(false);
  const [scannedDocument, setScannedDocument] = useState<any>(null);

  const { create } = useTFileRelation();

  const { uploadAsync: uploadToS3 } = useTFile({
    onProgress: ({ tempId, ...uploadStatus }) => {
      console.debug("onProgress", { tempId, uploadStatus });
    },
    onSuccess: async ({ tempId, data }) => {
      console.debug("onSuccess", { tempId, data });
      setTouchedValue("files", [
        {
          url: data.url,
          name: data.filename,
          filename: data.filename,
          bytes: data.bytes,
          typeId: data.typeId,
        },
      ]);
    },
    onError: ({ error }) => {
      console.error("Error uploading file:", error);
    },
  });

  const [
    tvaSelfLiquidation,
    amountExclTaxes,
    amountTaxes,
    companyContactId,
    id,
    isOverhead,
  ] = watch([
    "tvaSelfLiquidation",
    "amountExclTaxes",
    "amountTaxes",
    "companyContactId",
    "id",
    "isOverhead",
  ]);

  useEffect(() => {
    if (!id) return;
    const stored = localStorage.getItem("previousExpenseVAT");
    if (stored) {
      try {
        const parsed = JSON.parse(stored);
        setPreviousVAT(parsed);
      } catch (error) {
        console.error(
          "Failed to parse 'previousExpenseVAT' from localStorage:",
          error
        );
      }
    }
  }, [id]);

  useEffect(() => {
    if (!id) return;
    if (tvaSelfLiquidation) {
      if (amountTaxes !== "0") {
        const updated = {
          ...previousVAT,
          [id]: amountTaxes,
        };
        setPreviousVAT(updated);
        localStorage.setItem("previousExpenseVAT", JSON.stringify(updated));
      }
      setValue("amountTaxes", "0");
    } else {
      const restoredValue = previousVAT?.[id];
      if (restoredValue !== undefined) {
        setValue("amountTaxes", restoredValue);
      }
    }
  }, [tvaSelfLiquidation, id]);

  useEffect(() => {
    setValue(
      "amountTTC",
      BigInt(amountExclTaxes || 0) + BigInt(amountTaxes || 0)
    );
  }, [amountExclTaxes, amountTaxes]);

  useEffect(() => {
    setContactInfo(
      (companyContactId &&
        companyContactAutocomplete?.options.find(
          (contact) => contact?.value === companyContactId
        )) ||
        undefined
    );
  }, [companyContactId]);

  const handleFilesSelected = async (files: File[]) => {
    if (files.length === 0) return;
    setUploading(true);

    try {
      const file = files[0];

      const { tFile } = await uploadToS3({
        file,
        documentTypeKey: $Enums.LDocumentKey.expense,
      });
      setScannedDocument(tFile);
    } catch (error) {
      console.error("Erreur lors de l'upload:", error);
    } finally {
      setUploading(false);
    }
  };

  const handFileSelected = () => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = "application/pdf,image/*";
    input.onchange = (e) =>
      handleFilesSelected(
        Array.from((e.target as HTMLInputElement).files || [])
      );
    input.click();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    // Simuler la vérification de numérisation
    setIsScanned(true);
  };

  return (
    <div className="space-y-6 p-6">
      {/* Conteneur principal : Grille 2 colonnes */}
      <div className="grid grid-cols-2 gap-8">
        {/* 📌 Colonne Gauche : Informations sur la Facture */}
        <div className="bg-white border rounded-lg p-6 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            📄 Informations Facture
          </h3>

          {/* Type de pièce et Horodatage */}
          <div className="flex flex-col justify-between">
            <LabelledVTextField
              label="Horodatage interne"
              {...register("code")}
              disabled
              readOnly
              placeholder="Généré automatiquement"
              showHelp={true}
              helperText={
                <div className="space-y-4">
                  <h3 className="font-medium">Pourquoi cet horodatage</h3>

                  <div className="space-y-2">
                    <h4 className="font-medium">1. Le principe</h4>
                    <ul className="list-disc list-inside text-sm">
                      <li>
                        Pour permettre un suivi logique des facture,
                        l'entreprise suit un principe de{" "}
                      </li>
                      <li>
                        numérotation incrémentielle des factures entrantes.
                      </li>
                      <li>
                        Permet de suivre d'éventuels trous dans la numérotation
                        et de les judtifier.
                      </li>
                    </ul>
                  </div>
                </div>
              }
            />
            <LabelledSelectInput
              label="Type de pièce"
              placeholder="Sélectionner un type"
              options={invoiceTypes.autocomplete.options}
              helperText={<HelpOnDocumentType />}
              {...register("documentTypeId")}
            />
          </div>

          {/* Référence et Date */}
          <div className="flex flex-col gap-4 mt-4">
            <LabelledDateField
              label="Date du document"
              control={control}
              {...register("documentDateTz")}
            />

            <LabelledDateField
              label="Date de réception"
              control={control}
              {...register("receptionDateTz")}
            />
          </div>

          {/* Options */}
          <h4 className="text-md font-medium text-gray-700 mt-6">⚙️ Options</h4>
          <div className="space-y-3 mt-2">
            <LabelledCheckBox
              infoTextSide="right"
              infoText="Choisissez si cette charge doit etre affectée à un ou plusieurs chantiers, ou aux frais généraux de l'entreprise."
              label="Frais généraux"
              {...register("isOverhead")}
            />
            <LabelledCheckBox
              infoTextSide="right"
              infoText={`Le dispositif de l'autoliquidation de TVA consiste pour l'entreprise à ne pas collecter la TVA
                auprès du client en lui facturant uniquement le montant hors taxe.
                Ainsi, il revient au client de payer lui-même la taxe au trésor public.`}
              label="Autoliquidation de TVA"
              {...register("isVatSelfLiquidation")}
            />
            <LabelledCheckBox
              label="Paiement direct par MOA"
              {...register("isDirectPayment")}
            />
          </div>

          {/* Montants */}
          <h4 className="text-md font-medium text-gray-700 mt-6">
            💰 Montants
          </h4>
          <div className="flex flex-col gap-4 mt-2">
            <InputAmount
              label="Montant HT"
              control={control}
              name="amountExclTaxes"
            />
            <InputAmount
              label="Montant TVA"
              control={control}
              name="amountTaxes"
              disabled={tvaSelfLiquidation}
            />
            <InputAmount
              control={control}
              label="TVA non déductible"
              name="montantTVANonDed"
            />
            <InputAmount
              control={control}
              disabled={true}
              label="Montant TTC"
              name="amountTTC"
            />
          </div>
        </div>

        {/* 📌 Colonne Droite : Informations sur l'Émetteur */}
        <div className="bg-white border rounded-lg p-6 shadow-sm flex flex-col gap-2 shrink-0">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            🏢 Émetteur
          </h3>

          <LabelledSelectInput
            label="Code contact"
            {...register("companyContactId")}
            placeholder="Sélectionner un émetteur"
            options={companyContactAutocomplete?.options}
          />

          <div className="grid grid-cols-1 gap-4 mt-4">
            <LabelledVTextField
              label="Libellé sur la facture"
              {...register("description")}
            />
          </div>
          <div className="grid grid-cols-1 gap-4 mt-4">
            <LabelledVTextField
              label="Réference Emetteur"
              {...register("senderReference")}
            />
          </div>
          {contactInfo && (
            <div className="mt-8">
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <User className="size-5" />
                  <div>
                    <h2 className="text-xl font-semibold text-gray-800 flex gap-x-2">
                      {[
                        contactInfo?.code,
                        contactInfo?.name,
                        contactInfo?.sirenNumber,
                      ]
                        .filter(Boolean)
                        .map((item) => (
                          <span key={item}>{item}</span>
                        ))}
                    </h2>
                    {contactInfo?.createdAt && (
                      <p className="text-sm text-gray-500">
                        Client depuis{" "}
                        {new Date(contactInfo.createdAt).getFullYear()}
                      </p>
                    )}
                  </div>
                </div>

                <ContactInfo
                  title="Adresse postale"
                  icon={<MapPin className="size-5  mt-2" />}
                  value={[
                    contactInfo?.address,
                    contactInfo?.postalCode,
                    contactInfo?.city,
                  ]
                    .filter(Boolean)
                    .map((item) => (
                      <span key={item}>{item}</span>
                    ))}
                />
                <ContactInfo
                  title="Email"
                  icon={<Mail className="size-5 mt-2" />}
                  value={contactInfo?.email}
                />
                <ContactInfo
                  title="Téléphone"
                  icon={<Phone className="size-5 mt-2" />}
                  value={contactInfo?.phone}
                />
              </div>
            </div>
          )}

          {/* Zone de drop */}
          <div
            className={`
              relative p-6 mt-6 mb-6 rounded-lg border-2 border-dashed transition-colors
              ${isDragging ? "border-blue-500 bg-blue-50" : "border-gray-300"}
              ${isScanned ? "bg-green-50" : "bg-gray-50"}
            `}
            onDragOver={(e) => {
              e.preventDefault();
              setIsDragging(true);
            }}
            onDragLeave={() => setIsDragging(false)}
            onDrop={handleDrop}
            onClick={handFileSelected}
          >
            <div className="flex items-center justify-center">
              <div className="text-center">
                <FileText
                  className={`w-12 h-12 mx-auto mb-4 ${
                    isScanned ? "text-green-500" : "text-gray-400"
                  }`}
                />
                <h3
                  className={`text-lg font-medium mb-2 ${
                    isScanned ? "text-green-700" : "text-gray-700"
                  }`}
                >
                  {isScanned
                    ? "Document numérisé"
                    : "Déposez votre facture ici"}
                </h3>
                <p
                  className={`text-sm ${
                    isScanned ? "text-green-600" : "text-gray-500"
                  }`}
                >
                  {isScanned
                    ? "La facture a été correctement numérisée"
                    : "Glissez-déposez votre fichier ou cliquez pour sélectionner"}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white border rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          📊 Répartition par bon de commande
        </h3>
        <DistributionByOrder expenseId={id} />
      </div>

      {isOverhead ? (
        <div className="bg-white border rounded-lg p-6 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            📊 Répartition Budgétaire
          </h3>
          <BudgetDistribution
            fraisChantier={isOverhead}
            amount={amountExclTaxes}
          />
        </div>
      ) : (
        <div className="bg-white border rounded-lg p-6 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            📊 Répartition par projet
          </h3>
          <DistributionByProject expenseId={id} />
        </div>
      )}
    </div>
  );
}

const ContactInfo = ({
  icon,
  title,
  value,
  placeholder,
}: {
  icon: ReactNode;
  title: string;
  value?: ReactNode | string;
  placeholder?: string;
}) => {
  return (
    <div className="flex space-x-3">
      {icon}
      <div>
        <h3 className="font-medium text-gray-700">{title}</h3>
        {value ? (
          <p className="text-gray-600 flex flex-col">{value}</p>
        ) : (
          <p className="text-gray-600">
            {placeholder ?? "Information non renseignée"}
          </p>
        )}
      </div>
    </div>
  );
};
