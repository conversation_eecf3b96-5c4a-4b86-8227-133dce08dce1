import { centimesToEuros } from "@ui/components/input-amount";
import React from "react";

// Tableau de suivi des budgets
const BudgetDistributionle = ({ amount }: { amount: number }) => (
  <div className=" overflow-hidden">
    <table className="w-full text-sm border border-gray-300">
      <thead className="bg-gray-50 border border-gray-300">
        <tr>
          <th className="px-4 py-2 text-left border border-gray-300border border-gray-300">
            Budget
          </th>
          <th className="px-4 py-2 text-right border border-gray-300">
            Affectation H.T.
          </th>
        </tr>
      </thead>
      <tbody className="divide-y divide-gray-200 bg-white border border-gray-300">
        <tr>
          <td className="px-4 py-2 border border-gray-300">Frais généraux</td>
          <td className="px-4 py-2 text-right border border-gray-300">
            {centimesToEuros(amount) || "0,00"} €
          </td>
        </tr>
      </tbody>
    </table>
  </div>
);

export function BudgetDistribution(
  { fraisChantier, amount } = { fraisChantier: false, amount: 0 }
) {
  return (
    <div className="space-y-6">
      {fraisChantier ? (
        <BudgetDistributionle amount={amount} />
      ) : (
        <BudgetDistributionle amount={amount} />
      )}
    </div>
  );
}
