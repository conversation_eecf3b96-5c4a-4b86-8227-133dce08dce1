import type { TExpense } from "@repo/database/client";
import { PageComponentProps } from "@repo/types";
import { TreeTable } from "@shared/components/common/TreeTable";
import { useLInvoiceType } from "@shared/hooks/models";
import { useCurrentUser } from "@shared/hooks/models/custom/useCurrentUser";
import { useRouter } from "custom-router";
import { Download, Plus, Wallet } from "lucide-react";
import React, { useState } from "react";
import { FilterSection } from "../components/common/FilterSection";
import { PlaceholderPageIndex } from "../components/common/FilterSectionMap";
import { TabPageModel } from "../components/mainonglets/TabPageModel";

type TExpenseWithRelations = TExpense & {
  project?: { id: string; name: string };
  companyContact?: { id: string; name: string };
};

export const columns = ({
  invoiceTypes,
}: {
  invoiceTypes: ReturnType<typeof useLInvoiceType>;
}) => [
  {
    key: "code",
    header: "Code",
    width: 80,
    sortable: true,
    align: "center" as const,
  },
  {
    key: "documentDateTz",
    header: "Date du document ",
    width: 80,
    sortable: true,
    align: "center" as const,
    type: "date",
  },
  {
    key: "receptionDateTz",
    header: "Date de réception",
    width: 80,
    sortable: true,
    align: "center" as const,
    type: "date",
  },
  {
    key: "etat",
    header: "État",
    width: 80,
    sortable: true,
    align: "center" as const,
    render: (_row: TExpenseWithRelations) => "En cours",
  },
  {
    key: "prestataire",
    header: "Prestataire",
    width: 180,
    sortable: true,
    align: "left" as const,
    render: (row: TExpenseWithRelations) => row.companyContact?.name,
  },
  {
    key: "chantier",
    header: "Chantier",
    width: 250,
    sortable: true,
    align: "left" as const,
    render: (row: TExpenseWithRelations) => row.project?.name,
  },
  {
    key: "documentTypeId",
    header: "Type de document",
    width: 150,
    sortable: true,
    align: "right" as const,
    render: (row: TExpenseWithRelations) => {
      const type = invoiceTypes?.autocomplete.options?.find(
        (option) => option.value === row?.documentTypeId
      )?.label;
      if (!type) return null;
      return (
        <span
          className={`
              px-2 py-1 rounded-full text-xs font-medium whitespace-nowrap
              ${
                type === "client"
                  ? "bg-blue-100 text-blue-700"
                  : type === "prospect"
                    ? "bg-orange-100 text-orange-700"
                    : "bg-gray-100 text-gray-700"
              }
            `}
        >
          {type}
        </span>
      );
    },
  },
  {
    key: "senderReference",
    header: "Origine",
    width: 120,
    sortable: true,
    align: "right" as const,
  },
  {
    key: "amountExclTaxes",
    header: "Montant HT",
    width: 80,
    sortable: true,
    align: "right" as const,
    type: "amount",
  },
  {
    key: "amountTTC",
    header: "Montant TTC",
    width: 80,
    sortable: true,
    align: "right" as const,
    value: (row: TExpenseWithRelations) =>
      (row.amountExclTaxes || BigInt(0)) + (row.amountTaxes || BigInt(0)),
    type: "amount",
  },
  {
    key: "resteDu",
    header: "Reste dû",
    width: 80,
    sortable: true,
    align: "right" as const,
    type: "amount",
  },
];

type ExpensesProps = PageComponentProps<TExpenseWithRelations>;

export function Expenses({ data, onClickNew, onDelete }: ExpensesProps) {
  const router = useRouter();
  const items = data?.items;
  const invoiceTypes = useLInvoiceType({ autocomplete: true });
  const { isAllowedTo } = useCurrentUser();

  const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
  const [searchTerm, setSearchTerm] = useState("");
  const [lastSelectedIndex, setLastSelectedIndex] = useState<number>(-1);

  const handleRowClick = (rowId: string) => {
    const charge = items.find((exp) => exp.id === rowId);
    router.push(`/expenses/${rowId}`);
  };

  const handleDelete = (payload: { id: Array<string> }) => {
    onDelete(payload);
    setSelectedRows(new Set());
  };

  const handleExportExcel = () => {
    // TODO: Implement export functionality
  };

  return (
    <>
      <TabPageModel
        title="Charges"
        icon={Wallet}
        actions={[
          {
            icon: Plus,
            label: "Importer une charge",
            onClick: onClickNew,
            variant: "contained",
            disabled: !isAllowedTo("create", "TExpense"),
          },
          {
            icon: Download,
            label: "Exporter",
            onClick: handleExportExcel,
            variant: "outlined",
            disabled: !isAllowedTo("read", "TExpense"),
          },
        ]}
        dataStatPanel={
          <div className="text-center py-12 text-gray-500">
            Module de statistiques en cours de développement
          </div>
        }
      >
        <div className="space-y-4">
          <FilterSection
            showDateFilter
            onSearch={setSearchTerm}
            selectedRows={selectedRows}
            onAdd={onClickNew}
            onEdit={handleRowClick}
            onDelete={handleDelete}
            onExportExcel={handleExportExcel}
            placeholderSetIndex={PlaceholderPageIndex.charges} // ← ICI : placeholder spécifique à "charges"
            onFilterChange={() => {
              /** @todo implement filter change handler */
            }}
          />

          <TreeTable
            data={items}
            columns={columns({ invoiceTypes })}
            selectedRows={selectedRows}
            onRowSelectChange={({ selectedRows }) => {
              setSelectedRows(selectedRows);
            }}
            onRowClick={handleRowClick}
            expandedRows={expandedRows}
            sumConfig={{
              countField: "code",
              countLabel: "charge",
              sumFields: ["amountExclTaxes", "amountTTC", "netToPay"],
            }}
          />
        </div>
      </TabPageModel>
    </>
  );
}
