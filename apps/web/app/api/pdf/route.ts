import { NextRequest, NextResponse } from "next/server";
import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON>, PDFOptions } from "puppeteer";

export interface PDFDownloadRequest {
  url: string;
  options?: Partial<PDFOptions>;
  waitForSelector?: string;
  customCSS?: string;
  removeElements?: string[];
  filename?: string;
}

export interface PDFDownloadResponse {
  success: boolean;
  message?: string;
  error?: string;
}

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body: PDFDownloadRequest = await request.json();

    const { url, options = {}, waitForSelector, removeElements = [] } = body;

    if (!url) {
      return NextResponse.json({ message: "URL is required" }, { status: 400 });
    }

    let browser: Browser | null = null;

    try {
      browser = await puppeteer.launch({
        headless: true,
        args: [
          "--no-sandbox",
          "--disable-setuid-sandbox",
          "--disable-dev-shm-usage",
          "--disable-accelerated-2d-canvas",
          "--no-first-run",
          "--no-zygote",
          "--disable-gpu",
        ],
      });

      const page: Page = await browser.newPage();

      // Set viewport to a common desktop size
      await page.setViewport({
        width: 2480,
        height: 3508,
        deviceScaleFactor: 0.8,
      });

      await page.goto(url, {
        waitUntil: "networkidle0",
        timeout: 60000,
      });

      if (waitForSelector) {
        await page.waitForSelector(waitForSelector, { timeout: 10000 });
      }

      if (removeElements.length > 0) {
        await page.evaluate((selectors: string[]) => {
          selectors.forEach((selector: string) => {
            const elements = document.querySelectorAll(selector);
            elements.forEach((el: Element) => el.remove());
          });
        }, removeElements);
      }

      const pdfOptions: PDFOptions = {
        width: 2480,
        height: 3508,
        printBackground: true,
        margin: {
          top: "0.5cm",
          right: "0.5cm",
          bottom: "0.5cm",
          left: "0.5cm",
        },
        ...options,
      };

      const pdf: Buffer = Buffer.from(await page.pdf(pdfOptions));

      return new NextResponse(pdf, {
        status: 200,
        headers: {
          "Content-Type": "application/pdf",
          "Content-Disposition": 'attachment; filename="page.pdf"',
          "Content-Length": pdf.length.toString(),
        },
      });
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  } catch (error: unknown) {
    console.error("PDF generation error:", error);
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error occurred";

    return NextResponse.json(
      {
        message: "Failed to generate PDF",
        error: errorMessage,
      },
      { status: 500 }
    );
  }
}
