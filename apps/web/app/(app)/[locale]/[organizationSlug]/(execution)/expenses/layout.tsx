"use client";

import { useExpenseEB } from "@shared/books/expenseEB/ExpenseEB";
import { NewExpenseEB } from "@shared/books/expenseEB/NewExpenseEB";
import { useEditorBook } from "@shared/components/common/editorbook2";
import {
  type TExpenseCreateSchemaWithFiles,
  useTExpense,
  useTFileRelation,
} from "@shared/hooks/models";
import { useListSearchParams } from "@shared/hooks/useListSearchParams";
import { useLocalStorageFilter } from "@shared/hooks/useLocalStorageFilter";
import { Expenses } from "@shared/pages/Expenses";
import { useRouter } from "custom-router";
import type { PropsWithChildren } from "react";
import { useState } from "react";

export default function ExpensesLayout({ children }: PropsWithChildren) {
  const router = useRouter();

  const [importModal, setImportModal] = useState<boolean>(false);
  const { listParams, setListParams } = useListSearchParams();
  const { filters, setFilters } = useLocalStorageFilter();
  const { list, create, update, deleteMany } = useTExpense({
    ...listParams,
    filters,
  });

  const { openEditor, closeEditor } = useEditorBook();
  const { defaultProps } = useExpenseEB();
  const { create: createTFileRelation } = useTFileRelation();

  const onUpdate = async (data: any) => {
    return update({
      id: data.id,
      amountExclTaxes: data.amountExclTaxes,
      amountTaxes: data.amountTaxes,
    });
  };

  return (
    <>
      <Expenses
        data={list.data}
        loading={list.isLoading}
        onUpdate={onUpdate}
        onDelete={deleteMany}
        filters={filters}
        onSearch={setListParams}
        onFilterUpdate={setFilters}
        onClickNew={() => setImportModal(true)}
      />

      {importModal && (
        <NewExpenseEB
          isOpen={true}
          onClose={() => {
            setImportModal(false);
          }}
          onImport={async ({ data, file }: any) => {
            if (!data) {
              // If no data, it means the user has skipped the import
              setImportModal(false);
              const newExpense = await create({});
              router.push(`/expenses/${newExpense.id}`);
              return;
            }

            console.info("Importing data:", { data, file });

            const newExpensePayload: TExpenseCreateSchemaWithFiles = {
              documentDateTz:
                data?.invoiceDetails?.invoiceDate ||
                new Date().toISOString().split("T")[0],
              senderReference: data?.invoiceDetails?.invoiceNumber || "",
              amountExclTaxes: data?.summary?.totalBeforeTax?.value || 0,
              amountTaxes: data?.summary?.tax?.value || 0,
              files: file ? [file] : [],
            };
            const newExpense = await create(newExpensePayload);
            await createTFileRelation({
              fileId: file?.id,
              linkedId: newExpense.id,
              linkedModel: "TExpense",
            });
            setImportModal(false);
            router.push(`/expenses/${newExpense.id}`);
          }}
        />
      )}
      {children}
    </>
  );
}
