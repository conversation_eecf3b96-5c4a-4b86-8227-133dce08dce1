/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { TQuotationCompositionCreatecompositionIdsInputObjectSchema } from "./TQuotationCompositionCreatecompositionIdsInput.schema";
import { TQuotationCompositionCreatedmIdsInputObjectSchema } from "./TQuotationCompositionCreatedmIdsInput.schema";

type SchemaType =
  z.ZodType<Prisma.TQuotationCompositionCreateManyOrganizationInput>;
export const TQuotationCompositionCreateManyOrganizationInputObjectSchema: SchemaType =
  z
    .object({
      index: z.union([z.coerce.number(), z.null()]).optional().nullable(),
      id: z.string().optional(),
      deleted: z.union([z.boolean(), z.null()]).optional().nullable(),
      deletedAt: z
        .union([
          z.union([z.date(), z.string().datetime().optional()]),
          z.null(),
        ])
        .optional()
        .nullable(),
      createdAt: z
        .union([
          z.union([z.date(), z.string().datetime().optional()]),
          z.null(),
        ])
        .optional()
        .nullable(),
      updatedAt: z
        .union([
          z.union([z.date(), z.string().datetime().optional()]),
          z.null(),
        ])
        .optional()
        .nullable(),
      reference: z.union([z.coerce.number(), z.null()]).optional().nullable(),
      quotationId: z.string(),
      designation: z.union([z.string(), z.null()]).optional().nullable(),
      declaredUnitId: z
        .union([z.coerce.number(), z.null()])
        .optional()
        .nullable(),
      unitPrice: z.union([z.coerce.number(), z.null()]).optional().nullable(),
      vatRate: z.union([z.coerce.number(), z.null()]).optional().nullable(),
      level: z.union([z.coerce.number(), z.null()]).optional().nullable(),
      title: z.union([z.string(), z.null()]).optional().nullable(),
      articleType: z.union([z.coerce.number(), z.null()]).optional().nullable(),
      calculatedQuantity: z
        .union([z.boolean(), z.null()])
        .optional()
        .nullable(),
      expectedQuality: z
        .union([z.coerce.number(), z.null()])
        .optional()
        .nullable(),
      calculatedUnitId: z
        .union([z.coerce.number(), z.null()])
        .optional()
        .nullable(),
      quantity: z.union([z.coerce.number(), z.null()]).optional().nullable(),
      option: z.union([z.boolean(), z.null()]).optional().nullable(),
      parentId: z.union([z.coerce.number(), z.null()]).optional().nullable(),
      essential: z.union([z.boolean(), z.null()]).optional().nullable(),
      fallRatio: z.union([z.coerce.number(), z.null()]).optional().nullable(),
      hourId: z.union([z.coerce.number(), z.null()]).optional().nullable(),
      materialId: z.union([z.string(), z.null()]).optional().nullable(),
      productId: z.union([z.string(), z.null()]).optional().nullable(),
      minutes: z.union([z.coerce.number(), z.null()]).optional().nullable(),
      forcedTitle: z.union([z.boolean(), z.null()]).optional().nullable(),
      stampArticleId: z
        .union([z.coerce.number(), z.null()])
        .optional()
        .nullable(),
      workId: z.union([z.string(), z.null()]).optional().nullable(),
      dmIds: z
        .union([
          z.lazy(() => TQuotationCompositionCreatedmIdsInputObjectSchema),
          z.coerce.number().array(),
        ])
        .optional(),
      deletionLocked: z.union([z.boolean(), z.null()]).optional().nullable(),
      compositionIds: z
        .union([
          z.lazy(
            () => TQuotationCompositionCreatecompositionIdsInputObjectSchema
          ),
          z.coerce.number().array(),
        ])
        .optional(),
    })
    .strict() as SchemaType;
