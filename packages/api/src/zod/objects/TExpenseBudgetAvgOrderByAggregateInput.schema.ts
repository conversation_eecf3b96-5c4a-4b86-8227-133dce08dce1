/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { SortOrderSchema } from "../enums/SortOrder.schema";

type SchemaType = z.ZodType<Prisma.TExpenseBudgetAvgOrderByAggregateInput>;
export const TExpenseBudgetAvgOrderByAggregateInputObjectSchema: SchemaType = z
  .object({
    index: z.lazy(() => SortOrderSchema).optional(),
    amountExclTax: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
    budgetLibraryId: z
      .lazy(() => SortOrderSchema)
      .optional()
      .optional(),
  })
  .strict() as SchemaType;
