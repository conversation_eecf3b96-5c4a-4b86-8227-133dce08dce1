/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { NullableJsonNullValueInputSchema } from "../enums/NullableJsonNullValueInput.schema";
import { InvitationCreateNestedManyWithoutOrganizationInputObjectSchema } from "./InvitationCreateNestedManyWithoutOrganizationInput.schema";
import { MemberCreateNestedManyWithoutOrganizationInputObjectSchema } from "./MemberCreateNestedManyWithoutOrganizationInput.schema";
import { OrganizationCreatecompanyIdsInputObjectSchema } from "./OrganizationCreatecompanyIdsInput.schema";
import { OrganizationCreatewallpapersIdsInputObjectSchema } from "./OrganizationCreatewallpapersIdsInput.schema";
import { OrganizationCreatewebsitesInputObjectSchema } from "./OrganizationCreatewebsitesInput.schema";
import { RoleCreateNestedManyWithoutOrganizationInputObjectSchema } from "./RoleCreateNestedManyWithoutOrganizationInput.schema";
import { SandboxDailySiteCreateNestedManyWithoutOrganizationInputObjectSchema } from "./SandboxDailySiteCreateNestedManyWithoutOrganizationInput.schema";
import { SandboxSdayCreateNestedManyWithoutOrganizationInputObjectSchema } from "./SandboxSdayCreateNestedManyWithoutOrganizationInput.schema";
import { SDailySiteCreateNestedManyWithoutOrganizationInputObjectSchema } from "./SDailySiteCreateNestedManyWithoutOrganizationInput.schema";
import { SDayCreateNestedManyWithoutOrganizationInputObjectSchema } from "./SDayCreateNestedManyWithoutOrganizationInput.schema";
import { SMonthCreateNestedManyWithoutOrganizationInputObjectSchema } from "./SMonthCreateNestedManyWithoutOrganizationInput.schema";
import { SMonthGuyCreateNestedManyWithoutOrganizationInputObjectSchema } from "./SMonthGuyCreateNestedManyWithoutOrganizationInput.schema";
import { SMonthWorksiteCreateNestedManyWithoutOrganizationInputObjectSchema } from "./SMonthWorksiteCreateNestedManyWithoutOrganizationInput.schema";
import { STasksCreateNestedManyWithoutOrganizationInputObjectSchema } from "./STasksCreateNestedManyWithoutOrganizationInput.schema";
import { SWorksiteCreateNestedManyWithoutOrganizationInputObjectSchema } from "./SWorksiteCreateNestedManyWithoutOrganizationInput.schema";
import { TAccountingCategoryCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TAccountingCategoryCreateNestedManyWithoutOrganizationInput.schema";
import { TAdministrativeLibraryCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TAdministrativeLibraryCreateNestedManyWithoutOrganizationInput.schema";
import { TAIConversationCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TAIConversationCreateNestedManyWithoutOrganizationInput.schema";
import { TAIMessageCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TAIMessageCreateNestedManyWithoutOrganizationInput.schema";
import { TArticleCompositionCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TArticleCompositionCreateNestedManyWithoutOrganizationInput.schema";
import { TBankAccountCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TBankAccountCreateNestedManyWithoutOrganizationInput.schema";
import { TBudgetCategoryCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TBudgetCategoryCreateNestedManyWithoutOrganizationInput.schema";
import { TBusinessCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TBusinessCreateNestedManyWithoutOrganizationInput.schema";
import { TCommitmentCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TCommitmentCreateNestedManyWithoutOrganizationInput.schema";
import { TCompanyContactCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TCompanyContactCreateNestedManyWithoutOrganizationInput.schema";
import { TCompanySettingsCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TCompanySettingsCreateNestedManyWithoutOrganizationInput.schema";
import { TComposedWorkCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TComposedWorkCreateNestedManyWithoutOrganizationInput.schema";
import { TDebtCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TDebtCreateNestedManyWithoutOrganizationInput.schema";
import { TEmailCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TEmailCreateNestedManyWithoutOrganizationInput.schema";
import { TEquipmentCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TEquipmentCreateNestedManyWithoutOrganizationInput.schema";
import { TEventCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TEventCreateNestedManyWithoutOrganizationInput.schema";
import { TExpenseBudgetCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TExpenseBudgetCreateNestedManyWithoutOrganizationInput.schema";
import { TExpenseCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TExpenseCreateNestedManyWithoutOrganizationInput.schema";
import { TExpenseOrderCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TExpenseOrderCreateNestedManyWithoutOrganizationInput.schema";
import { TExpenseProjectCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TExpenseProjectCreateNestedManyWithoutOrganizationInput.schema";
import { TFileCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TFileCreateNestedManyWithoutOrganizationInput.schema";
import { TGenericProductLibraryCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TGenericProductLibraryCreateNestedManyWithoutOrganizationInput.schema";
import { TGenericProductStampCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TGenericProductStampCreateNestedManyWithoutOrganizationInput.schema";
import { TGuaranteeCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TGuaranteeCreateNestedManyWithoutOrganizationInput.schema";
import { TGuyCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TGuyCreateNestedManyWithoutOrganizationInput.schema";
import { THourAssignmentCreateNestedManyWithoutOrganizationInputObjectSchema } from "./THourAssignmentCreateNestedManyWithoutOrganizationInput.schema";
import { THourlyWorkCreateNestedManyWithoutOrganizationInputObjectSchema } from "./THourlyWorkCreateNestedManyWithoutOrganizationInput.schema";
import { THourStampCreateNestedManyWithoutOrganizationInputObjectSchema } from "./THourStampCreateNestedManyWithoutOrganizationInput.schema";
import { THoursCreateNestedManyWithoutOrganizationInputObjectSchema } from "./THoursCreateNestedManyWithoutOrganizationInput.schema";
import { TInvoiceCompositionCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TInvoiceCompositionCreateNestedManyWithoutOrganizationInput.schema";
import { TInvoiceCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TInvoiceCreateNestedManyWithoutOrganizationInput.schema";
import { TLeaveCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TLeaveCreateNestedManyWithoutOrganizationInput.schema";
import { TLogTraceCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TLogTraceCreateNestedManyWithoutOrganizationInput.schema";
import { TMaterialLibraryCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TMaterialLibraryCreateNestedManyWithoutOrganizationInput.schema";
import { TMaterialStampCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TMaterialStampCreateNestedManyWithoutOrganizationInput.schema";
import { TMetricDetailCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TMetricDetailCreateNestedManyWithoutOrganizationInput.schema";
import { TNetSalaryTeamCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TNetSalaryTeamCreateNestedManyWithoutOrganizationInput.schema";
import { TObservationCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TObservationCreateNestedManyWithoutOrganizationInput.schema";
import { TOrderCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TOrderCreateNestedManyWithoutOrganizationInput.schema";
import { TOrderedProductsCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TOrderedProductsCreateNestedManyWithoutOrganizationInput.schema";
import { TOrderedServiceCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TOrderedServiceCreateNestedManyWithoutOrganizationInput.schema";
import { TPackagingCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TPackagingCreateNestedManyWithoutOrganizationInput.schema";
import { TProdTeamCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TProdTeamCreateNestedManyWithoutOrganizationInput.schema";
import { TProductWorkCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TProductWorkCreateNestedManyWithoutOrganizationInput.schema";
import { TProjectCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TProjectCreateNestedManyWithoutOrganizationInput.schema";
import { TQuotationCompositionCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TQuotationCompositionCreateNestedManyWithoutOrganizationInput.schema";
import { TQuotationCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TQuotationCreateNestedManyWithoutOrganizationInput.schema";
import { TQuoteCategoryCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TQuoteCategoryCreateNestedManyWithoutOrganizationInput.schema";
import { TReminderCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TReminderCreateNestedManyWithoutOrganizationInput.schema";
import { TSafetyTimingCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TSafetyTimingCreateNestedManyWithoutOrganizationInput.schema";
import { TSalaryCostCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TSalaryCostCreateNestedManyWithoutOrganizationInput.schema";
import { TSiteContactCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TSiteContactCreateNestedManyWithoutOrganizationInput.schema";
import { TSitePurchaseOrdersCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TSitePurchaseOrdersCreateNestedManyWithoutOrganizationInput.schema";
import { TStampUsageCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TStampUsageCreateNestedManyWithoutOrganizationInput.schema";
import { TStepLibraryCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TStepLibraryCreateNestedManyWithoutOrganizationInput.schema";
import { TStudiesCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TStudiesCreateNestedManyWithoutOrganizationInput.schema";
import { TSubcontractorHoursCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TSubcontractorHoursCreateNestedManyWithoutOrganizationInput.schema";
import { TSupplierCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TSupplierCreateNestedManyWithoutOrganizationInput.schema";
import { TSupplierOrderFormsCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TSupplierOrderFormsCreateNestedManyWithoutOrganizationInput.schema";
import { TSupplierProductsCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TSupplierProductsCreateNestedManyWithoutOrganizationInput.schema";
import { TTaskListCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TTaskListCreateNestedManyWithoutOrganizationInput.schema";
import { TTeamStatsCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TTeamStatsCreateNestedManyWithoutOrganizationInput.schema";
import { TTimeTrackingCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TTimeTrackingCreateNestedManyWithoutOrganizationInput.schema";
import { TVehiclesCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TVehiclesCreateNestedManyWithoutOrganizationInput.schema";
import { TWorkLibraryCreateNestedManyWithoutOrganizationInputObjectSchema } from "./TWorkLibraryCreateNestedManyWithoutOrganizationInput.schema";

const literalSchema = z.union([z.string(), z.coerce.number(), z.boolean()]);
const jsonSchema: z.ZodType<Prisma.InputJsonValue> = z.lazy(() =>
  z.union([
    literalSchema,
    z.array(jsonSchema.nullable()),
    z.record(jsonSchema.nullable()),
  ])
);

type SchemaType =
  z.ZodType<Prisma.OrganizationCreateWithoutTBankOperationInput>;
export const OrganizationCreateWithoutTBankOperationInputObjectSchema: SchemaType =
  z
    .object({
      id: z.string().optional(),
      deleted: z.union([z.boolean(), z.null()]).optional().nullable(),
      deletedAt: z
        .union([
          z.union([z.date(), z.string().datetime().optional()]),
          z.null(),
        ])
        .optional()
        .nullable(),
      createdAt: z
        .union([
          z.union([z.date(), z.string().datetime().optional()]),
          z.null(),
        ])
        .optional()
        .nullable(),
      updatedAt: z
        .union([
          z.union([z.date(), z.string().datetime().optional()]),
          z.null(),
        ])
        .optional()
        .nullable(),
      name: z.union([z.string(), z.null()]).optional().nullable(),
      slug: z.union([z.string(), z.null()]).optional().nullable(),
      theoreticalPurchaseAmount: z
        .union([z.coerce.number(), z.null()])
        .optional()
        .nullable(),
      theoreticalModAmount: z
        .union([z.coerce.number(), z.null()])
        .optional()
        .nullable(),
      dsCoefficient: z
        .union([z.coerce.number(), z.null()])
        .optional()
        .nullable(),
      generalExpenses: z
        .union([z.coerce.number(), z.null()])
        .optional()
        .nullable(),
      prCoefficient: z
        .union([z.coerce.number(), z.null()])
        .optional()
        .nullable(),
      marginCoefficient: z
        .union([z.coerce.number(), z.null()])
        .optional()
        .nullable(),
      pvCoefficient: z
        .union([z.coerce.number(), z.null()])
        .optional()
        .nullable(),
      dailySstPrice: z
        .union([z.coerce.number(), z.null()])
        .optional()
        .nullable(),
      storekeeperName: z.union([z.string(), z.null()]).optional().nullable(),
      storekeeperPhone: z.union([z.string(), z.null()]).optional().nullable(),
      logo: z.union([z.string(), z.null()]).optional().nullable(),
      decennialInsurer: z.union([z.string(), z.null()]).optional().nullable(),
      insurancePolicy: z.union([z.string(), z.null()]).optional().nullable(),
      rcsCity: z.union([z.string(), z.null()]).optional().nullable(),
      decennialContractName: z
        .union([z.string(), z.null()])
        .optional()
        .nullable(),
      quoteMention: z.union([z.string(), z.null()]).optional().nullable(),
      frameContractDuration: z
        .union([z.coerce.bigint(), z.null()])
        .optional()
        .nullable(),
      kbisValidity: z
        .union([z.coerce.number(), z.null()])
        .optional()
        .nullable(),
      urssafValidity: z
        .union([z.coerce.number(), z.null()])
        .optional()
        .nullable(),
      urssafVerificationValidity: z
        .union([z.coerce.number(), z.null()])
        .optional()
        .nullable(),
      professionalLiabilityValidity: z
        .union([z.coerce.number(), z.null()])
        .optional()
        .nullable(),
      ribValidity: z.union([z.coerce.number(), z.null()]).optional().nullable(),
      nominativeListValidity: z
        .union([z.coerce.number(), z.null()])
        .optional()
        .nullable(),
      otherDocumentValidity: z
        .union([z.coerce.bigint(), z.null()])
        .optional()
        .nullable(),
      firstProgressNumber: z
        .union([z.coerce.bigint(), z.null()])
        .optional()
        .nullable(),
      firstSituationNumber: z
        .union([z.coerce.bigint(), z.null()])
        .optional()
        .nullable(),
      firstInvoiceNumber: z
        .union([z.coerce.bigint(), z.null()])
        .optional()
        .nullable(),
      firstDepositNumber: z
        .union([z.coerce.bigint(), z.null()])
        .optional()
        .nullable(),
      firstCreditNoteNumber: z
        .union([z.coerce.bigint(), z.null()])
        .optional()
        .nullable(),
      firstForecastNumber: z
        .union([z.coerce.bigint(), z.null()])
        .optional()
        .nullable(),
      invoiceMention: z.union([z.string(), z.null()]).optional().nullable(),
      theoreticalMaterialAmount: z
        .union([z.coerce.number(), z.null()])
        .optional()
        .nullable(),
      coefFc: z.union([z.coerce.number(), z.null()]).optional().nullable(),
      logotype: z
        .union([
          z.custom<Buffer | Uint8Array>((data) => data instanceof Uint8Array),
          z.null(),
        ])
        .optional()
        .nullable(),
      apeCode: z.union([z.string(), z.null()]).optional().nullable(),
      parentCompany: z
        .union([z.coerce.number(), z.null()])
        .optional()
        .nullable(),
      password: z.union([z.string(), z.null()]).optional().nullable(),
      ceeMention: z.union([z.string(), z.null()]).optional().nullable(),
      contactId: z.union([z.string(), z.null()]).optional().nullable(),
      companyIds: z
        .union([
          z.lazy(() => OrganizationCreatecompanyIdsInputObjectSchema),
          z.coerce.number().array(),
        ])
        .optional(),
      phoneCode: z.union([z.string(), z.null()]).optional().nullable(),
      subtitle: z.union([z.string(), z.null()]).optional().nullable(),
      bridgeUser: z.union([z.string(), z.null()]).optional().nullable(),
      lastSituationNumber: z
        .union([z.coerce.bigint(), z.null()])
        .optional()
        .nullable(),
      lastForecastNumber: z
        .union([z.coerce.bigint(), z.null()])
        .optional()
        .nullable(),
      accountingPlatformEmail: z
        .union([z.string(), z.null()])
        .optional()
        .nullable(),
      automaticAccountingSending: z
        .union([z.boolean(), z.null()])
        .optional()
        .nullable(),
      tmp: z.union([z.boolean(), z.null()]).optional().nullable(),
      websites: z
        .union([
          z.lazy(() => OrganizationCreatewebsitesInputObjectSchema),
          z.string().array(),
        ])
        .optional(),
      wallpapersIds: z
        .union([
          z.lazy(() => OrganizationCreatewallpapersIdsInputObjectSchema),
          z.coerce.number().array(),
        ])
        .optional(),
      metadata: z
        .union([z.lazy(() => NullableJsonNullValueInputSchema), jsonSchema])
        .optional(),
      members: z
        .lazy(() => MemberCreateNestedManyWithoutOrganizationInputObjectSchema)
        .optional()
        .optional(),
      invitations: z
        .lazy(
          () => InvitationCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      roles: z
        .lazy(() => RoleCreateNestedManyWithoutOrganizationInputObjectSchema)
        .optional()
        .optional(),
      SandboxDailySite: z
        .lazy(
          () =>
            SandboxDailySiteCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      SandboxSday: z
        .lazy(
          () => SandboxSdayCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      SDailySite: z
        .lazy(
          () => SDailySiteCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      SDay: z
        .lazy(() => SDayCreateNestedManyWithoutOrganizationInputObjectSchema)
        .optional()
        .optional(),
      SMonth: z
        .lazy(() => SMonthCreateNestedManyWithoutOrganizationInputObjectSchema)
        .optional()
        .optional(),
      SMonthGuy: z
        .lazy(
          () => SMonthGuyCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      SMonthWorksite: z
        .lazy(
          () =>
            SMonthWorksiteCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      STasks: z
        .lazy(() => STasksCreateNestedManyWithoutOrganizationInputObjectSchema)
        .optional()
        .optional(),
      SWorksite: z
        .lazy(
          () => SWorksiteCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TAccountingCategory: z
        .lazy(
          () =>
            TAccountingCategoryCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TAdministrativeLibrary: z
        .lazy(
          () =>
            TAdministrativeLibraryCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TAIConversation: z
        .lazy(
          () =>
            TAIConversationCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TAIMessage: z
        .lazy(
          () => TAIMessageCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TArticleComposition: z
        .lazy(
          () =>
            TArticleCompositionCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TBankAccount: z
        .lazy(
          () => TBankAccountCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TBudgetCategory: z
        .lazy(
          () =>
            TBudgetCategoryCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TBusiness: z
        .lazy(
          () => TBusinessCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TExpenseProject: z
        .lazy(
          () =>
            TExpenseProjectCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TExpenseOrder: z
        .lazy(
          () =>
            TExpenseOrderCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TExpenseBudget: z
        .lazy(
          () =>
            TExpenseBudgetCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TCommitment: z
        .lazy(
          () => TCommitmentCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TCompanyContact: z
        .lazy(
          () =>
            TCompanyContactCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TCompanySettings: z
        .lazy(
          () =>
            TCompanySettingsCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TComposedWork: z
        .lazy(
          () =>
            TComposedWorkCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TDebt: z
        .lazy(() => TDebtCreateNestedManyWithoutOrganizationInputObjectSchema)
        .optional()
        .optional(),
      TEmail: z
        .lazy(() => TEmailCreateNestedManyWithoutOrganizationInputObjectSchema)
        .optional()
        .optional(),
      TEquipment: z
        .lazy(
          () => TEquipmentCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TEvent: z
        .lazy(() => TEventCreateNestedManyWithoutOrganizationInputObjectSchema)
        .optional()
        .optional(),
      TExpense: z
        .lazy(
          () => TExpenseCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TFile: z
        .lazy(() => TFileCreateNestedManyWithoutOrganizationInputObjectSchema)
        .optional()
        .optional(),
      TGenericProductLibrary: z
        .lazy(
          () =>
            TGenericProductLibraryCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TGenericProductStamp: z
        .lazy(
          () =>
            TGenericProductStampCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TGuarantee: z
        .lazy(
          () => TGuaranteeCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TGuy: z
        .lazy(() => TGuyCreateNestedManyWithoutOrganizationInputObjectSchema)
        .optional()
        .optional(),
      THourAssignment: z
        .lazy(
          () =>
            THourAssignmentCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      THourlyWork: z
        .lazy(
          () => THourlyWorkCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      THours: z
        .lazy(() => THoursCreateNestedManyWithoutOrganizationInputObjectSchema)
        .optional()
        .optional(),
      THourStamp: z
        .lazy(
          () => THourStampCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TInvoice: z
        .lazy(
          () => TInvoiceCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TInvoiceComposition: z
        .lazy(
          () =>
            TInvoiceCompositionCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TLeave: z
        .lazy(() => TLeaveCreateNestedManyWithoutOrganizationInputObjectSchema)
        .optional()
        .optional(),
      TLogTrace: z
        .lazy(
          () => TLogTraceCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TMaterialLibrary: z
        .lazy(
          () =>
            TMaterialLibraryCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TMaterialStamp: z
        .lazy(
          () =>
            TMaterialStampCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TMetricDetail: z
        .lazy(
          () =>
            TMetricDetailCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TNetSalaryTeam: z
        .lazy(
          () =>
            TNetSalaryTeamCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TObservation: z
        .lazy(
          () => TObservationCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TOrder: z
        .lazy(() => TOrderCreateNestedManyWithoutOrganizationInputObjectSchema)
        .optional()
        .optional(),
      TOrderedProducts: z
        .lazy(
          () =>
            TOrderedProductsCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TOrderedService: z
        .lazy(
          () =>
            TOrderedServiceCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TPackaging: z
        .lazy(
          () => TPackagingCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TProdTeam: z
        .lazy(
          () => TProdTeamCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TProductWork: z
        .lazy(
          () => TProductWorkCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TProject: z
        .lazy(
          () => TProjectCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TQuotation: z
        .lazy(
          () => TQuotationCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TQuotationComposition: z
        .lazy(
          () =>
            TQuotationCompositionCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TQuoteCategory: z
        .lazy(
          () =>
            TQuoteCategoryCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TReminder: z
        .lazy(
          () => TReminderCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TSafetyTiming: z
        .lazy(
          () =>
            TSafetyTimingCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TSalaryCost: z
        .lazy(
          () => TSalaryCostCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TSiteContact: z
        .lazy(
          () => TSiteContactCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TSitePurchaseOrders: z
        .lazy(
          () =>
            TSitePurchaseOrdersCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TStampUsage: z
        .lazy(
          () => TStampUsageCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TStepLibrary: z
        .lazy(
          () => TStepLibraryCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TStudies: z
        .lazy(
          () => TStudiesCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TSubcontractorHours: z
        .lazy(
          () =>
            TSubcontractorHoursCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TSupplier: z
        .lazy(
          () => TSupplierCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TSupplierOrderForms: z
        .lazy(
          () =>
            TSupplierOrderFormsCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TSupplierProducts: z
        .lazy(
          () =>
            TSupplierProductsCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TTaskList: z
        .lazy(
          () => TTaskListCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TTeamStats: z
        .lazy(
          () => TTeamStatsCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TTimeTracking: z
        .lazy(
          () =>
            TTimeTrackingCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TVehicles: z
        .lazy(
          () => TVehiclesCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
      TWorkLibrary: z
        .lazy(
          () => TWorkLibraryCreateNestedManyWithoutOrganizationInputObjectSchema
        )
        .optional()
        .optional(),
    })
    .strict() as SchemaType;
