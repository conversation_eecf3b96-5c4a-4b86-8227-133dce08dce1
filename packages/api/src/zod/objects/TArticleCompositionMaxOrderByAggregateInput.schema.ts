/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { SortOrderSchema } from "../enums/SortOrder.schema";

type SchemaType = z.ZodType<Prisma.TArticleCompositionMaxOrderByAggregateInput>;
export const TArticleCompositionMaxOrderByAggregateInputObjectSchema: SchemaType =
  z
    .object({
      index: z.lazy(() => SortOrderSchema).optional(),
      id: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      deleted: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      deletedAt: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      createdAt: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      updatedAt: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      organizationId: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      hourId: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      quoteCompoId: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      purchasePrice: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      productId: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      materialId: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      label: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      workCompoId: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      typeId: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      parentStepId: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      quantityDouble: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      minutes: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      dropCoefficient: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      keyId: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      essential: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      bridgeClient: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
      bridgePwd: z
        .lazy(() => SortOrderSchema)
        .optional()
        .optional(),
    })
    .strict() as SchemaType;
