/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { BoolNullableFilterObjectSchema } from "./BoolNullableFilter.schema";
import { DateTimeNullableFilterObjectSchema } from "./DateTimeNullableFilter.schema";
import { IntNullableFilterObjectSchema } from "./IntNullableFilter.schema";
import { StringFilterObjectSchema } from "./StringFilter.schema";

type SchemaType = z.ZodType<Prisma.TInvoiceCompositionScalarWhereInput>;
export const TInvoiceCompositionScalarWhereInputObjectSchema: SchemaType = z
  .object({
    AND: z
      .union([
        z.lazy(() => TInvoiceCompositionScalarWhereInputObjectSchema),
        z.lazy(() => TInvoiceCompositionScalarWhereInputObjectSchema).array(),
      ])
      .optional(),
    OR: z
      .lazy(() => TInvoiceCompositionScalarWhereInputObjectSchema)
      .array()
      .optional(),
    NOT: z
      .union([
        z.lazy(() => TInvoiceCompositionScalarWhereInputObjectSchema),
        z.lazy(() => TInvoiceCompositionScalarWhereInputObjectSchema).array(),
      ])
      .optional(),
    index: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    id: z
      .union([z.lazy(() => StringFilterObjectSchema), z.string()])
      .optional(),
    deleted: z
      .union([
        z.lazy(() => BoolNullableFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    deletedAt: z
      .union([
        z.lazy(() => DateTimeNullableFilterObjectSchema),
        z.union([z.date(), z.string().datetime().optional()]),
        z.null(),
      ])
      .optional()
      .nullable(),
    createdAt: z
      .union([
        z.lazy(() => DateTimeNullableFilterObjectSchema),
        z.union([z.date(), z.string().datetime().optional()]),
        z.null(),
      ])
      .optional()
      .nullable(),
    updatedAt: z
      .union([
        z.lazy(() => DateTimeNullableFilterObjectSchema),
        z.union([z.date(), z.string().datetime().optional()]),
        z.null(),
      ])
      .optional()
      .nullable(),
    organizationId: z
      .union([z.lazy(() => StringFilterObjectSchema), z.string()])
      .optional(),
    associatedInvoiceId: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    estimateCompositionId: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    monthProgress: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    cumulativeProgress: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    parentId: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    essential: z
      .union([
        z.lazy(() => BoolNullableFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    type: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    keyId: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
  })
  .strict() as SchemaType;
