/******************************************************************************
 * This file was generated by ZenStack CLI.
 ******************************************************************************/

/* eslint-disable */
// @ts-nocheck

import type { Prisma } from "@prisma/client";
import { z } from "zod";
import { BoolNullableFilterObjectSchema } from "./BoolNullableFilter.schema";
import { DateTimeNullableFilterObjectSchema } from "./DateTimeNullableFilter.schema";
import { IntNullableFilterObjectSchema } from "./IntNullableFilter.schema";
import { OrganizationScalarRelationFilterObjectSchema } from "./OrganizationScalarRelationFilter.schema";
import { OrganizationWhereInputObjectSchema } from "./OrganizationWhereInput.schema";
import { StringFilterObjectSchema } from "./StringFilter.schema";
import { StringNullableFilterObjectSchema } from "./StringNullableFilter.schema";
import { TVehiclesWhereInputObjectSchema } from "./TVehiclesWhereInput.schema";

type SchemaType = z.ZodType<Prisma.TVehiclesWhereUniqueInput>;
export const TVehiclesWhereUniqueInputObjectSchema: SchemaType = z
  .object({
    id: z.string().optional(),
    AND: z
      .union([
        z.lazy(() => TVehiclesWhereInputObjectSchema),
        z.lazy(() => TVehiclesWhereInputObjectSchema).array(),
      ])
      .optional(),
    OR: z
      .lazy(() => TVehiclesWhereInputObjectSchema)
      .array()
      .optional()
      .optional(),
    NOT: z
      .union([
        z.lazy(() => TVehiclesWhereInputObjectSchema),
        z.lazy(() => TVehiclesWhereInputObjectSchema).array(),
      ])
      .optional(),
    deleted: z
      .union([
        z.lazy(() => BoolNullableFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    deletedAt: z
      .union([
        z.lazy(() => DateTimeNullableFilterObjectSchema),
        z.union([z.date(), z.string().datetime().optional()]),
        z.null(),
      ])
      .optional()
      .nullable(),
    createdAt: z
      .union([
        z.lazy(() => DateTimeNullableFilterObjectSchema),
        z.union([z.date(), z.string().datetime().optional()]),
        z.null(),
      ])
      .optional()
      .nullable(),
    updatedAt: z
      .union([
        z.lazy(() => DateTimeNullableFilterObjectSchema),
        z.union([z.date(), z.string().datetime().optional()]),
        z.null(),
      ])
      .optional()
      .nullable(),
    organizationId: z
      .union([z.lazy(() => StringFilterObjectSchema), z.string()])
      .optional(),
    registration: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    circulationDateTz: z
      .union([
        z.lazy(() => DateTimeNullableFilterObjectSchema),
        z.union([z.date(), z.string().datetime().optional()]),
        z.null(),
      ])
      .optional()
      .nullable(),
    brand: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    model: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    vehicleType: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    purchaseOrRentalDate: z
      .union([
        z.lazy(() => DateTimeNullableFilterObjectSchema),
        z.union([z.date(), z.string().datetime().optional()]),
        z.null(),
      ])
      .optional()
      .nullable(),
    monthlyRent: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    description: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    color: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    fiscalPower: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    fuel: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    price: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    borrowingCompany: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    whenTz: z
      .union([
        z.lazy(() => DateTimeNullableFilterObjectSchema),
        z.union([z.date(), z.string().datetime().optional()]),
        z.null(),
      ])
      .optional()
      .nullable(),
    site: z
      .union([
        z.lazy(() => IntNullableFilterObjectSchema),
        z.coerce.number(),
        z.null(),
      ])
      .optional()
      .nullable(),
    isOwner: z
      .union([
        z.lazy(() => BoolNullableFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    isRent: z
      .union([
        z.lazy(() => BoolNullableFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    isFinancialLoss: z
      .union([
        z.lazy(() => BoolNullableFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    isMaintenance: z
      .union([
        z.lazy(() => BoolNullableFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    isTires: z
      .union([
        z.lazy(() => BoolNullableFilterObjectSchema),
        z.boolean(),
        z.null(),
      ])
      .optional()
      .nullable(),
    associatedFuelCard: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    contractualMileage: z
      .union([
        z.lazy(() => StringNullableFilterObjectSchema),
        z.string(),
        z.null(),
      ])
      .optional()
      .nullable(),
    returnDateTz: z
      .union([
        z.lazy(() => DateTimeNullableFilterObjectSchema),
        z.union([z.date(), z.string().datetime().optional()]),
        z.null(),
      ])
      .optional()
      .nullable(),
    nextTechnicalInspectionTz: z
      .union([
        z.lazy(() => DateTimeNullableFilterObjectSchema),
        z.union([z.date(), z.string().datetime().optional()]),
        z.null(),
      ])
      .optional()
      .nullable(),
    purchaseOrRentalDateTz: z
      .union([
        z.lazy(() => DateTimeNullableFilterObjectSchema),
        z.union([z.date(), z.string().datetime().optional()]),
        z.null(),
      ])
      .optional()
      .nullable(),
    organization: z
      .union([
        z.lazy(() => OrganizationScalarRelationFilterObjectSchema),
        z.lazy(() => OrganizationWhereInputObjectSchema),
      ])
      .optional(),
  })
  .strict() as SchemaType;
